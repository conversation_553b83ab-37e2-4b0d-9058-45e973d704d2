import service from '@/utils/request'

// ==================== 开票信息管理 API ====================

// @Summary 创建开票信息
// @Produce application/json
// @Param data body CreateInvoiceInfoRequest true "创建开票信息"
// @Router /invoice/createInvoiceInfo [post]
export const createInvoiceInfo = (data) => {
  return service({
    url: '/invoice/createInvoiceInfo',
    method: 'post',
    data: data
  })
}

// @Summary 更新开票信息
// @Produce application/json
// @Param data body UpdateInvoiceInfoRequest true "更新开票信息"
// @Router /invoice/updateInvoiceInfo [put]
export const updateInvoiceInfo = (data) => {
  return service({
    url: '/invoice/updateInvoiceInfo',
    method: 'put',
    data: data
  })
}

// @Summary 删除开票信息
// @Produce application/json
// @Param data body DeleteInvoiceInfoRequest true "删除开票信息"
// @Router /invoice/deleteInvoiceInfo [delete]
export const deleteInvoiceInfo = (data) => {
  return service({
    url: '/invoice/deleteInvoiceInfo',
    method: 'delete',
    data: data
  })
}

// @Summary 根据ID获取开票信息
// @Produce application/json
// @Param id query uint true "开票信息ID"
// @Router /invoice/findInvoiceInfo [get]
export const getInvoiceInfoById = (id) => {
  return service({
    url: '/invoice/findInvoiceInfo',
    method: 'get',
    params: { id }
  })
}

// @Summary 获取开票信息列表
// @Produce application/json
// @Param data query InvoiceInfoSearch true "获取开票信息列表"
// @Router /invoice/getInvoiceInfoList [get]
export const getInvoiceInfoList = (params) => {
  return service({
    url: '/invoice/getInvoiceInfoList',
    method: 'get',
    params: params
  })
}

// @Summary 获取默认开票信息
// @Produce application/json
// @Router /invoice/getDefaultInvoiceInfo [get]
export const getDefaultInvoiceInfo = () => {
  return service({
    url: '/invoice/getDefaultInvoiceInfo',
    method: 'get'
  })
}

// ==================== 发票管理 API ====================

// @Summary 申请开票
// @Produce application/json
// @Param data body ApplyInvoiceRequest true "申请开票"
// @Router /invoice/applyInvoice [post]
export const applyInvoice = (data) => {
  return service({
    url: '/invoice/applyInvoice',
    method: 'post',
    data: data
  })
}

// @Summary 更新发票状态
// @Produce application/json
// @Param data body UpdateInvoiceStatusRequest true "更新发票状态"
// @Router /invoice/updateInvoiceStatus [put]
export const updateInvoiceStatus = (data) => {
  return service({
    url: '/invoice/updateInvoiceStatus',
    method: 'put',
    data: data
  })
}

// @Summary 删除发票
// @Produce application/json
// @Param data body DeleteInvoiceRequest true "删除发票"
// @Router /invoice/deleteInvoice [delete]
export const deleteInvoice = (data) => {
  return service({
    url: '/invoice/deleteInvoice',
    method: 'delete',
    data: data
  })
}

// @Summary 根据ID获取发票
// @Produce application/json
// @Param id query uint true "发票ID"
// @Router /invoice/findInvoice [get]
export const getInvoiceById = (id) => {
  return service({
    url: '/invoice/findInvoice',
    method: 'get',
    params: { id }
  })
}

// @Summary 获取发票列表
// @Produce application/json
// @Param data query InvoiceSearch true "获取发票列表"
// @Router /invoice/getInvoiceList [get]
export const getInvoiceList = (params) => {
  return service({
    url: '/invoice/getInvoiceList',
    method: 'get',
    params: params
  })
}

// @Summary 管理员获取发票列表
// @Produce application/json
// @Param data query InvoiceSearch true "获取发票列表"
// @Router /invoice/getAdminInvoiceList [get]
export const getAdminInvoiceList = (params) => {
  return service({
    url: '/invoice/getAdminInvoiceList',
    method: 'get',
    params: params
  })
}

// @Summary 获取发票统计
// @Produce application/json
// @Router /invoice/getInvoiceStatistics [get]
export const getInvoiceStatistics = () => {
  return service({
    url: '/invoice/getInvoiceStatistics',
    method: 'get'
  })
}

// @Summary 管理员获取发票统计
// @Produce application/json
// @Router /invoice/getAdminInvoiceStatistics [get]
export const getAdminInvoiceStatistics = () => {
  return service({
    url: '/invoice/getAdminInvoiceStatistics',
    method: 'get'
  })
}

// ==================== 常量定义 ====================

// 发票类型常量
export const INVOICE_TYPE = {
  PERSONAL: 'personal',  // 个人
  COMPANY: 'company'     // 企业
}

// 发票状态常量
export const INVOICE_STATUS = {
  PENDING: 'pending',       // 待处理
  PROCESSING: 'processing', // 处理中
  ISSUED: 'issued',        // 已开票
  FAILED: 'failed'         // 开票失败
}

// 发票类型显示文本映射
export const INVOICE_TYPE_TEXT = {
  [INVOICE_TYPE.PERSONAL]: '个人',
  [INVOICE_TYPE.COMPANY]: '企业'
}

// 发票状态显示文本映射
export const INVOICE_STATUS_TEXT = {
  [INVOICE_STATUS.PENDING]: '待处理',
  [INVOICE_STATUS.PROCESSING]: '处理中',
  [INVOICE_STATUS.ISSUED]: '已开票',
  [INVOICE_STATUS.FAILED]: '开票失败'
}

// 发票类型颜色映射
export const INVOICE_TYPE_COLOR = {
  [INVOICE_TYPE.PERSONAL]: 'primary',
  [INVOICE_TYPE.COMPANY]: 'success'
}

// 发票状态颜色映射
export const INVOICE_STATUS_COLOR = {
  [INVOICE_STATUS.PENDING]: 'warning',
  [INVOICE_STATUS.PROCESSING]: 'primary',
  [INVOICE_STATUS.ISSUED]: 'success',
  [INVOICE_STATUS.FAILED]: 'danger'
}

// 发票状态图标映射
export const INVOICE_STATUS_ICON = {
  [INVOICE_STATUS.PENDING]: 'Clock',
  [INVOICE_STATUS.PROCESSING]: 'Loading',
  [INVOICE_STATUS.ISSUED]: 'Check',
  [INVOICE_STATUS.FAILED]: 'Close'
}
