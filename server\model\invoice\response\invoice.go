package response

import (
	"whlxyc.cn/server/model/invoice"
)

// InvoiceInfoResponse 开票信息响应
type InvoiceInfoResponse struct {
	invoice.InvoiceInfo
}

// InvoiceInfoListResponse 开票信息列表响应
type InvoiceInfoListResponse struct {
	List     []InvoiceInfoResponse `json:"list"`     // 开票信息列表
	Total    int64                 `json:"total"`    // 总数
	Page     int                   `json:"page"`     // 当前页
	PageSize int                   `json:"pageSize"` // 每页大小
}

// InvoiceResponse 发票响应
type InvoiceResponse struct {
	invoice.Invoice
	CanDownload bool `json:"canDownload"` // 是否可下载
}

// InvoiceListResponse 发票列表响应
type InvoiceListResponse struct {
	List     []InvoiceResponse `json:"list"`     // 发票列表
	Total    int64             `json:"total"`    // 总数
	Page     int               `json:"page"`     // 当前页
	PageSize int               `json:"pageSize"` // 每页大小
}

// InvoiceStatisticsResponse 发票统计响应
type InvoiceStatisticsResponse struct {
	TotalInvoices    int64   `json:"totalInvoices"`    // 总发票数
	PendingInvoices  int64   `json:"pendingInvoices"`  // 待处理发票数
	IssuedInvoices   int64   `json:"issuedInvoices"`   // 已开票数
	FailedInvoices   int64   `json:"failedInvoices"`   // 开票失败数
	TotalAmount      float64 `json:"totalAmount"`      // 总金额
	IssuedAmount     float64 `json:"issuedAmount"`     // 已开票金额
}
