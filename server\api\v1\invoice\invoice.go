package invoice

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"whlxyc.cn/server/global"
	"whlxyc.cn/server/model/common/response"
	"whlxyc.cn/server/model/invoice/request"
	"whlxyc.cn/server/utils"
)

type InvoiceApi struct{}

// ApplyInvoice 申请开票
// @Tags Invoice
// @Summary 申请开票
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ApplyInvoiceRequest true "申请开票"
// @Success 200 {object} response.Response{msg=string} "申请成功"
// @Router /invoice/applyInvoice [post]
func (i *InvoiceApi) ApplyInvoice(c *gin.Context) {
	var req request.ApplyInvoiceRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	invoice, err := invoiceService.ApplyInvoice(&req, userID)
	if err != nil {
		global.DY_LOG.Error("申请开票失败!", zap.Error(err))
		response.FailWithMessage("申请失败: "+err.Error(), c)
		return
	}

	response.OkWithData(invoice, c)
}

// UpdateInvoiceStatus 更新发票状态
// @Tags Invoice
// @Summary 更新发票状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateInvoiceStatusRequest true "更新发票状态"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /invoice/updateInvoiceStatus [put]
func (i *InvoiceApi) UpdateInvoiceStatus(c *gin.Context) {
	var req request.UpdateInvoiceStatusRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = invoiceService.UpdateInvoiceStatus(&req)
	if err != nil {
		global.DY_LOG.Error("更新发票状态失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// GetInvoiceByID 根据ID获取发票
// @Tags Invoice
// @Summary 根据ID获取发票
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "发票ID"
// @Success 200 {object} response.Response{data=invoice.Invoice} "获取成功"
// @Router /invoice/findInvoice [get]
func (i *InvoiceApi) GetInvoiceByID(c *gin.Context) {
	var req struct {
		ID uint `form:"id" binding:"required"`
	}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	invoice, err := invoiceService.GetInvoiceByID(req.ID)
	if err != nil {
		global.DY_LOG.Error("查询发票失败!", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	response.OkWithData(invoice, c)
}

// GetInvoiceList 获取发票列表
// @Tags Invoice
// @Summary 获取发票列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.InvoiceSearch true "获取发票列表"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /invoice/getInvoiceList [get]
func (i *InvoiceApi) GetInvoiceList(c *gin.Context) {
	var req request.InvoiceSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	total, invoiceList, err := invoiceService.GetInvoiceList(&req)
	if err != nil {
		global.DY_LOG.Error("获取发票列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(response.PageResult{
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     invoiceList,
		Total:    total,
	}, c)
}

// GetAdminInvoiceList 管理员获取发票列表
// @Tags Invoice
// @Summary 管理员获取发票列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.InvoiceSearch true "获取发票列表"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /invoice/getAdminInvoiceList [get]
func (i *InvoiceApi) GetAdminInvoiceList(c *gin.Context) {
	var req request.InvoiceSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	total, invoiceList, err := invoiceService.GetInvoiceList(&req)
	if err != nil {
		global.DY_LOG.Error("获取发票列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(response.PageResult{
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     invoiceList,
		Total:    total,
	}, c)
}

// DeleteInvoice 删除发票
// @Tags Invoice
// @Summary 删除发票
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.DeleteInvoiceRequest true "删除发票"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /invoice/deleteInvoice [delete]
func (i *InvoiceApi) DeleteInvoice(c *gin.Context) {
	var req request.DeleteInvoiceRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = invoiceService.DeleteInvoice(&req, &userID)
	if err != nil {
		global.DY_LOG.Error("删除发票失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetInvoiceStatistics 获取发票统计
// @Tags Invoice
// @Summary 获取发票统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.InvoiceStatisticsResponse} "获取成功"
// @Router /invoice/getInvoiceStatistics [get]
func (i *InvoiceApi) GetInvoiceStatistics(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)

	stats, err := invoiceService.GetInvoiceStatistics(&userID)
	if err != nil {
		global.DY_LOG.Error("获取发票统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(stats, c)
}

// GetAdminInvoiceStatistics 管理员获取发票统计
// @Tags Invoice
// @Summary 管理员获取发票统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.InvoiceStatisticsResponse} "获取成功"
// @Router /invoice/getAdminInvoiceStatistics [get]
func (i *InvoiceApi) GetAdminInvoiceStatistics(c *gin.Context) {
	stats, err := invoiceService.GetInvoiceStatistics(nil)
	if err != nil {
		global.DY_LOG.Error("获取发票统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(stats, c)
}
