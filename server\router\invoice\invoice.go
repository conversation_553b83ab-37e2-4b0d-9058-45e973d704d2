package invoice

import (
	"github.com/gin-gonic/gin"
	"whlxyc.cn/server/api/v1"
	"whlxyc.cn/server/middleware"
)

type InvoiceRouter struct{}

// InitInvoiceRouter 初始化开票路由
func (r *InvoiceRouter) InitInvoiceRouter(Router *gin.RouterGroup) {
	invoiceRouter := Router.Group("invoice").Use(middleware.OperationRecord())
	invoiceRouterWithoutRecord := Router.Group("invoice")
	invoiceApi := v1.ApiGroupApp.InvoiceApiGroup

	{
		// 开票信息管理
		invoiceRouter.POST("createInvoiceInfo", invoiceApi.CreateInvoiceInfo)       // 创建开票信息
		invoiceRouter.PUT("updateInvoiceInfo", invoiceApi.UpdateInvoiceInfo)        // 更新开票信息
		invoiceRouter.DELETE("deleteInvoiceInfo", invoiceApi.DeleteInvoiceInfo)     // 删除开票信息
		invoiceRouterWithoutRecord.GET("findInvoiceInfo", invoiceApi.GetInvoiceInfoByID)         // 根据ID获取开票信息
		invoiceRouterWithoutRecord.GET("getInvoiceInfoList", invoiceApi.GetInvoiceInfoList)      // 获取开票信息列表
		invoiceRouterWithoutRecord.GET("getDefaultInvoiceInfo", invoiceApi.GetDefaultInvoiceInfo) // 获取默认开票信息

		// 发票管理
		invoiceRouter.POST("applyInvoice", invoiceApi.ApplyInvoice)                 // 申请开票
		invoiceRouter.PUT("updateInvoiceStatus", invoiceApi.UpdateInvoiceStatus)    // 更新发票状态
		invoiceRouter.DELETE("deleteInvoice", invoiceApi.DeleteInvoice)             // 删除发票
		invoiceRouterWithoutRecord.GET("findInvoice", invoiceApi.GetInvoiceByID)               // 根据ID获取发票
		invoiceRouterWithoutRecord.GET("getInvoiceList", invoiceApi.GetInvoiceList)            // 获取发票列表
		invoiceRouterWithoutRecord.GET("getAdminInvoiceList", invoiceApi.GetAdminInvoiceList)  // 管理员获取发票列表
		invoiceRouterWithoutRecord.GET("getInvoiceStatistics", invoiceApi.GetInvoiceStatistics) // 获取发票统计
		invoiceRouterWithoutRecord.GET("getAdminInvoiceStatistics", invoiceApi.GetAdminInvoiceStatistics) // 管理员获取发票统计
	}
}
