package meeting

import (
	"github.com/gin-gonic/gin"
	v1 "whlxyc.cn/server/api/v1"
	"whlxyc.cn/server/middleware"
)

type PaymentRouter struct{}

// InitPaymentRouter 初始化 Payment 路由信息
func (s *PaymentRouter) InitPaymentRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	paymentRouter := Router.Group("payment").Use(middleware.OperationRecord())
	paymentRouterWithoutRecord := Router.Group("payment")
	_ = PublicRouter // 暂时不使用公开路由

	var paymentApi = v1.ApiGroupApp.MeetingApiGroup.PaymentApi
	{
		paymentRouter.POST("createPayment", paymentApi.CreatePayment)             // 新建Payment
		paymentRouter.DELETE("deletePayment", paymentApi.DeletePayment)           // 删除Payment
		paymentRouter.DELETE("adminDeletePayment", paymentApi.AdminDeletePayment) // 管理员删除Payment
		paymentRouter.PUT("updatePayment", paymentApi.UpdatePayment)              // 更新Payment
		paymentRouter.PUT("adminUpdatePayment", paymentApi.AdminUpdatePayment)    // 管理员更新Payment
		paymentRouter.POST("refundPayment", paymentApi.RefundPayment)             // 退款
		paymentRouter.POST("adminRefundPayment", paymentApi.AdminRefundPayment)   // 管理员退款
	}
	{
		paymentRouterWithoutRecord.GET("findPayment", paymentApi.GetPaymentByID)                                    // 根据ID获取Payment
		paymentRouterWithoutRecord.GET("adminFindPayment", paymentApi.AdminGetPaymentByID)                          // 管理员根据ID获取Payment
		paymentRouterWithoutRecord.GET("getPaymentList", paymentApi.GetPaymentList)                                 // 获取Payment列表
		paymentRouterWithoutRecord.GET("adminGetPaymentList", paymentApi.AdminGetPaymentList)                       // 管理员获取Payment列表
		paymentRouterWithoutRecord.GET("getPaymentStatistics", paymentApi.GetPaymentStatistics)                     // 获取Payment统计
		paymentRouterWithoutRecord.GET("adminGetPaymentStatistics", paymentApi.AdminGetPaymentStatistics)           // 管理员获取Payment统计
		paymentRouterWithoutRecord.GET("getAvailablePaymentsForInvoice", paymentApi.GetAvailablePaymentsForInvoice) // 获取可用于开票的支付记录
	}
}
