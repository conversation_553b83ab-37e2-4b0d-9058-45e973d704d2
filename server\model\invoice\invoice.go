package invoice

import (
	"time"
	"whlxyc.cn/server/global"
	"whlxyc.cn/server/model/system"
)

// Invoice 发票 结构体
type Invoice struct {
	global.DY_MODEL
	UserID        uint         `json:"userId" form:"userId" gorm:"column:user_id;comment:用户ID;not null"`                                                                                                  // 用户ID
	PaymentID     uint         `json:"paymentId" form:"paymentId" gorm:"column:payment_id;comment:支付ID;not null"`                                                                                     // 支付ID
	InvoiceNumber string       `json:"invoiceNumber" form:"invoiceNumber" gorm:"column:invoice_number;comment:发票号码;unique"`                                                                          // 发票号码
	InvoiceTitle  string       `json:"invoiceTitle" form:"invoiceTitle" gorm:"column:invoice_title;comment:发票抬头;not null"`                                                                          // 发票抬头
	TaxNumber     string       `json:"taxNumber" form:"taxNumber" gorm:"column:tax_number;comment:税号"`                                                                                               // 税号
	Amount        float64      `json:"amount" form:"amount" gorm:"column:amount;comment:发票金额;type:decimal(10,2);not null"`                                                                          // 发票金额
	Status        string       `json:"status" form:"status" gorm:"column:status;type:enum('pending','processing','issued','failed');comment:发票状态;default:pending"`                                // 发票状态
	IssueDate     *time.Time   `json:"issueDate" form:"issueDate" gorm:"column:issue_date;comment:开票日期"`                                                                                            // 开票日期
	FilePath      string       `json:"filePath" form:"filePath" gorm:"column:file_path;comment:发票文件路径"`                                                                                             // 发票文件路径
	Notes         string       `json:"notes" form:"notes" gorm:"column:notes;type:text;comment:备注"`                                                                                                  // 备注
	User          system.SysUser `json:"user" gorm:"foreignKey:UserID"`                                                                                                                              // 用户信息
}

// TableName Invoice表名
func (Invoice) TableName() string {
	return "invoices"
}

// 发票状态常量
const (
	InvoiceStatusPending    = "pending"    // 待处理
	InvoiceStatusProcessing = "processing" // 处理中
	InvoiceStatusIssued     = "issued"     // 已开票
	InvoiceStatusFailed     = "failed"     // 开票失败
)
