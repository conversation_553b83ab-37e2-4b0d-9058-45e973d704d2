<template>
  <el-dialog
    v-model="visible"
    title="开票信息详情"
    width="600px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="invoice-info-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <el-icon><User /></el-icon>
          基本信息
        </h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="发票类型">
            <el-tag :type="INVOICE_TYPE_COLOR[invoiceInfoData.invoiceType]">
              {{ INVOICE_TYPE_TEXT[invoiceInfoData.invoiceType] }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发票抬头">
            <span class="invoice-title">{{ invoiceInfoData.invoiceTitle || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否默认">
            <el-tag v-if="invoiceInfoData.isDefault" type="success">
              <el-icon><Star /></el-icon>
              默认开票信息
            </el-tag>
            <span v-else class="text-gray-400">否</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(invoiceInfoData.CreatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(invoiceInfoData.UpdatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 企业信息 (仅企业类型显示) -->
      <div 
        class="detail-section" 
        v-if="invoiceInfoData.invoiceType === INVOICE_TYPE.COMPANY"
      >
        <h3 class="section-title">
          <el-icon><OfficeBuilding /></el-icon>
          企业信息
        </h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="税号">
            <span class="tax-number">{{ invoiceInfoData.taxNumber || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="公司电话">
            {{ invoiceInfoData.companyPhone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="开户银行">
            {{ invoiceInfoData.bankName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="银行账号">
            <span class="bank-account">{{ invoiceInfoData.bankAccount || '-' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 联系信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <el-icon><Phone /></el-icon>
          联系信息
        </h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="联系电话">
            <span class="contact-phone">{{ invoiceInfoData.contactPhone || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="联系邮箱">
            <span class="contact-email">{{ invoiceInfoData.contactEmail || '-' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 使用统计 -->
      <div class="detail-section" v-if="usageStats">
        <h3 class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          使用统计
        </h3>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ usageStats.totalInvoices }}</div>
            <div class="stat-label">总开票次数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ usageStats.totalAmount }}</div>
            <div class="stat-label">总开票金额</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ usageStats.lastUsed ? formatDate(usageStats.lastUsed) : '未使用' }}</div>
            <div class="stat-label">最后使用</div>
          </div>
        </div>
      </div>

      <!-- 操作提示 -->
      <div class="detail-section">
        <h3 class="section-title">
          <el-icon><InfoFilled /></el-icon>
          操作提示
        </h3>
        <div class="tips-content">
          <el-alert
            v-if="invoiceInfoData.isDefault"
            title="默认开票信息"
            description="此开票信息已设为默认，申请开票时会自动选择此信息"
            type="success"
            :closable="false"
            show-icon
          />
          <el-alert
            v-if="invoiceInfoData.invoiceType === INVOICE_TYPE.COMPANY && !invoiceInfoData.taxNumber"
            title="税号缺失"
            description="企业开票信息建议填写税号，以确保发票的合规性"
            type="warning"
            :closable="false"
            show-icon
          />
          <div class="tip-item">
            <el-icon class="tip-icon"><Edit /></el-icon>
            <span>您可以随时编辑此开票信息</span>
          </div>
          <div class="tip-item">
            <el-icon class="tip-icon"><Star /></el-icon>
            <span>设为默认后，申请开票时会自动选择此信息</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="editInvoiceInfo">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getInvoiceInfoById,
  INVOICE_TYPE,
  INVOICE_TYPE_TEXT,
  INVOICE_TYPE_COLOR
} from '@/api/invoice'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  invoiceInfoId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'edit'])

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const loading = ref(false)
const invoiceInfoData = ref({})
const usageStats = ref(null)

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.invoiceInfoId) {
    loadInvoiceInfoDetail()
  }
})

// 加载开票信息详情
const loadInvoiceInfoDetail = async () => {
  if (!props.invoiceInfoId) return
  
  try {
    loading.value = true
    const res = await getInvoiceInfoById(props.invoiceInfoId)
    
    if (res.code === 0) {
      invoiceInfoData.value = res.data
      // 这里可以加载使用统计
      loadUsageStats()
    }
  } catch (error) {
    console.error('加载开票信息详情失败:', error)
    ElMessage.error('加载开票信息详情失败')
  } finally {
    loading.value = false
  }
}

// 加载使用统计 (模拟数据)
const loadUsageStats = () => {
  // 这里应该调用实际的API获取统计数据
  usageStats.value = {
    totalInvoices: Math.floor(Math.random() * 20),
    totalAmount: (Math.random() * 10000).toFixed(2),
    lastUsed: invoiceInfoData.value.UpdatedAt
  }
}

// 编辑开票信息
const editInvoiceInfo = () => {
  emit('edit', invoiceInfoData.value)
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  invoiceInfoData.value = {}
  usageStats.value = null
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化日期
const formatDate = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.invoice-info-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.invoice-title {
  font-weight: 600;
  color: #409eff;
}

.tax-number,
.bank-account {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.contact-phone,
.contact-email {
  color: #409eff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.tips-content {
  space-y: 12px;
}

.tips-content .el-alert {
  margin-bottom: 12px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  color: #606266;
  font-size: 14px;
}

.tip-icon {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
