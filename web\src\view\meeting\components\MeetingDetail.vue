<template>
  <el-dialog
    v-model="dialogVisible"
    title="会议详情"
    width="1000px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="meeting-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="会议标题" :span="2">
          <span class="meeting-title">{{ meetingData.title }}</span>
        </el-descriptions-item>
        
        <el-descriptions-item label="开始时间">
          {{ formatDateTime(meetingData.startTime) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="结束时间">
          {{ formatDateTime(meetingData.endTime) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="会议地点">
          {{ meetingData.location || '未设置' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="会议状态">
          <el-tag :type="MEETING_STATUS_COLOR[meetingData.status]">
            {{ MEETING_STATUS_TEXT[meetingData.status] }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="创建者">
          {{ meetingData.creator?.nickName || meetingData.creator?.userName || '未知' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(meetingData.createdAt) }}
        </el-descriptions-item>

        <el-descriptions-item label="会议类型">
          {{ getTypeText(meetingData.type) }}
        </el-descriptions-item>

        <el-descriptions-item label="最大参与人数">
          {{ meetingData.maxParticipants || '无限制' }}
        </el-descriptions-item>

        <el-descriptions-item label="早鸟截止时间" :span="2">
          {{ formatDateTime(meetingData.earlyDeadline) }}
        </el-descriptions-item>

        <el-descriptions-item label="注册截止时间" :span="2">
          {{ formatDateTime(meetingData.registrationDeadline) }}
        </el-descriptions-item>

        <!-- 费用信息 -->
        <el-descriptions-item label="专家费用" :span="2">
          <div class="fee-info">
            <span class="fee-item">早鸟价: ¥{{ meetingData.expertFeeEarly }}</span>
            <span class="fee-item">正常价: ¥{{ meetingData.expertFeeNormal }}</span>
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="学生费用" :span="2">
          <div class="fee-info">
            <span class="fee-item">早鸟价: ¥{{ meetingData.studentFeeEarly }}</span>
            <span class="fee-item">正常价: ¥{{ meetingData.studentFeeNormal }}</span>
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="企业费用" :span="2">
          <div class="fee-info">
            <span class="fee-item">早鸟价: ¥{{ meetingData.corporateFeeEarly }}</span>
            <span class="fee-item">正常价: ¥{{ meetingData.corporateFeeNormal }}</span>
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="会议描述" :span="2">
          <div class="description-content">
            {{ meetingData.description || '无描述' }}
          </div>
        </el-descriptions-item>
        
        <el-descriptions-item label="会议纪要" :span="2" v-if="meetingData.notes">
          <div class="notes-content">
            {{ meetingData.notes }}
          </div>
        </el-descriptions-item>
        
        <el-descriptions-item label="附件" :span="2" v-if="attachments.length > 0">
          <div class="attachments-content">
            <el-tag
              v-for="(attachment, index) in attachments"
              :key="index"
              class="attachment-tag"
            >
              <el-link :href="attachment" target="_blank" :underline="false">
                {{ attachment }}
              </el-link>
            </el-tag>
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 已支付参与者列表 -->
      <div class="registrations-section">
        <h3 class="section-title">
          已支付参与者列表 ({{ registrations.length }})
        </h3>

        <el-table :data="registrations" style="width: 100%">
          <el-table-column prop="user.realName" label="姓名" width="120" />
          <el-table-column prop="user.phone" label="手机号" width="120" />
          <el-table-column prop="user.email" label="邮箱" width="180" />
          <el-table-column label="注册类型" width="100">
            <template #default="scope">
              <el-tag :type="getRegistrationTypeColor(scope.row.registrationType)">
                {{ getRegistrationTypeText(scope.row.registrationType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="费用金额" width="100">
            <template #default="scope">
              ¥{{ scope.row.feeAmount }}
            </template>
          </el-table-column>
          <el-table-column label="支付状态" width="100">
            <template #default="scope">
              <el-tag :type="getPaymentStatusColor(scope.row.paymentStatus)">
                {{ getPaymentStatusText(scope.row.paymentStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="注册时间" width="160">
            <template #default="scope">
              {{ formatDateTime(scope.row.registeredAt) }}
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="120">
            <template #default="scope">
              {{ scope.row.notes || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" v-if="canManageRegistrations">
            <template #default="scope">
              <el-button
                v-if="scope.row.paymentStatus === 'pending'"
                type="success"
                size="small"
                @click="updatePaymentStatus(scope.row, 'paid')"
              >
                确认支付
              </el-button>
              <el-button
                v-if="scope.row.paymentStatus === 'paid'"
                type="warning"
                size="small"
                @click="updatePaymentStatus(scope.row, 'refunded')"
              >
                退款
              </el-button>
              <el-button
                v-if="scope.row.paymentStatus === 'paid'"
                type="primary"
                size="small"
                @click="openInvoiceApply(scope.row)"
              >
                申请开票
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="canEdit"
          type="primary"
          @click="editMeeting"
        >
          编辑会议
        </el-button>
      </div>
    </template>


  </el-dialog>

  <!-- 开票申请组件 -->
  <InvoiceApply
    v-model:visible="invoiceApplyVisible"
    :meeting-info="invoiceApplyData"
    @success="onInvoiceApplySuccess"
  />
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getMeetingById,
  updateParticipantStatus,
  MEETING_STATUS_TEXT,
  MEETING_STATUS_COLOR
} from '@/api/meeting'
import { useUserStore } from '@/pinia/modules/user'
import InvoiceApply from '@/view/invoice/components/InvoiceApply.vue'

const emit = defineEmits(['refresh', 'edit'])

const userStore = useUserStore()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const meetingData = reactive({})
const registrations = ref([])

// 开票申请相关
const invoiceApplyVisible = ref(false)
const invoiceApplyData = ref({
  id: null,
  title: '',
  amount: 0,
  paymentId: null
})

// 计算属性
const attachments = computed(() => {
  if (!meetingData.attachments) return []
  try {
    return typeof meetingData.attachments === 'string' 
      ? JSON.parse(meetingData.attachments) 
      : meetingData.attachments
  } catch (e) {
    return []
  }
})

const canEdit = computed(() => {
  return meetingData.creatorId === userStore.userInfo.ID
})

const canManageRegistrations = computed(() => {
  return meetingData.creatorId === userStore.userInfo.ID
})

// 获取会议类型文本
const getTypeText = (type) => {
  const typeMap = {
    'online': '线上',
    'offline': '线下',
    'hybrid': '混合'
  }
  return typeMap[type] || type
}

// 获取注册类型文本和颜色
const getRegistrationTypeText = (type) => {
  const typeMap = {
    'expert': '专家',
    'student': '学生',
    'corporate': '企业代表'
  }
  return typeMap[type] || type
}

const getRegistrationTypeColor = (type) => {
  const colorMap = {
    'expert': 'success',
    'student': 'primary',
    'corporate': 'warning'
  }
  return colorMap[type] || 'info'
}

// 获取支付状态文本和颜色
const getPaymentStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'failed': '支付失败',
    'refunded': '已退款'
  }
  return statusMap[status] || status
}

const getPaymentStatusColor = (status) => {
  const colorMap = {
    'pending': 'warning',
    'paid': 'success',
    'failed': 'danger',
    'refunded': 'info'
  }
  return colorMap[status] || 'info'
}

// 打开对话框
const openDialog = async (meetingId) => {
  dialogVisible.value = true
  await getMeetingDetail(meetingId)
}

// 获取会议详情
const getMeetingDetail = async (meetingId) => {
  loading.value = true
  try {
    const res = await getMeetingById(meetingId)
    if (res.code === 0) {
      Object.assign(meetingData, res.data)
      registrations.value = res.data.participants || []
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    ElMessage.error('获取会议详情失败')
  } finally {
    loading.value = false
  }
}

// 更新支付状态
const updatePaymentStatus = async (registration, status) => {
  try {
    const res = await updateParticipantStatus({
      meetingId: meetingData.ID,
      status: status
    })

    if (res.code === 0) {
      ElMessage.success('支付状态更新成功')
      // 更新本地数据
      const index = registrations.value.findIndex(r => r.ID === registration.ID)
      if (index !== -1) {
        registrations.value[index].paymentStatus = status
      }
      emit('refresh')
    }
  } catch (error) {
    console.error('更新支付状态失败:', error)
    ElMessage.error('更新支付状态失败')
  }
}

// 编辑会议
const editMeeting = () => {
  emit('edit', meetingData)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  Object.keys(meetingData).forEach(key => {
    delete meetingData[key]
  })
  registrations.value = []
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 打开开票申请弹窗
const openInvoiceApply = (registration) => {
  invoiceApplyData.value = {
    id: meetingData.ID,
    title: meetingData.title,
    amount: registration.amount || 0,
    paymentId: registration.paymentId || registration.ID
  }
  invoiceApplyVisible.value = true
}

// 开票申请成功回调
const onInvoiceApplySuccess = (invoiceData) => {
  ElMessage.success('开票申请提交成功')
  // 这里可以添加其他成功后的逻辑
}

// 暴露方法
defineExpose({
  openDialog
})
</script>

<style scoped>
.meeting-detail {
  padding: 20px 0;
}

.meeting-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.description-content,
.notes-content {
  max-height: 120px;
  overflow-y: auto;
  line-height: 1.6;
  white-space: pre-wrap;
}

.attachments-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attachment-tag {
  margin: 0;
}

.fee-info {
  display: flex;
  gap: 20px;
  align-items: center;
}

.fee-item {
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

.registrations-section {
  margin-top: 30px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}
</style>
