package response

import (
	"whlxyc.cn/server/model/meeting"
)

// PaymentResponse 支付记录响应
type PaymentResponse struct {
	meeting.Payment
	UserName    string `json:"userName"`    // 用户名
	MeetingName string `json:"meetingName"` // 会议名称
}

// PaymentListResponse 支付记录列表响应
type PaymentListResponse struct {
	List     []PaymentResponse `json:"list"`     // 支付记录列表
	Total    int64             `json:"total"`    // 总数
	Page     int               `json:"page"`     // 当前页
	PageSize int               `json:"pageSize"` // 每页大小
}

// PaymentStatisticsResponse 支付统计响应
type PaymentStatisticsResponse struct {
	TotalPayments    int64   `json:"totalPayments"`    // 总支付笔数
	TotalAmount      float64 `json:"totalAmount"`      // 总支付金额
	SuccessPayments  int64   `json:"successPayments"`  // 成功支付笔数
	SuccessAmount    float64 `json:"successAmount"`    // 成功支付金额
	PendingPayments  int64   `json:"pendingPayments"`  // 待支付笔数
	PendingAmount    float64 `json:"pendingAmount"`    // 待支付金额
	RefundedPayments int64   `json:"refundedPayments"` // 已退款笔数
	RefundedAmount   float64 `json:"refundedAmount"`   // 已退款金额
	FailedPayments   int64   `json:"failedPayments"`   // 失败支付笔数
	FailedAmount     float64 `json:"failedAmount"`     // 失败支付金额
}

// PaymentMethodStatistics 支付方式统计
type PaymentMethodStatistics struct {
	PaymentMethod string  `json:"paymentMethod"` // 支付方式
	Count         int64   `json:"count"`         // 笔数
	Amount        float64 `json:"amount"`        // 金额
}

// DailyPaymentStatistics 每日支付统计
type DailyPaymentStatistics struct {
	Date   string  `json:"date"`   // 日期
	Count  int64   `json:"count"`  // 笔数
	Amount float64 `json:"amount"` // 金额
}
