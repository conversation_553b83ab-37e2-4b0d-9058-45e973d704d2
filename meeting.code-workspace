{"folders": [{"name": "🔧 Server (Go)", "path": "./server"}, {"name": "🎨 Web (Vue)", "path": "./web"}, {"name": "📚 Docs", "path": "./docs"}, {"path": "."}], "settings": {"go.gopath": "C:\\Users\\<USER>\\go\\pkg\\mod\\golang.org\\<EMAIL>-amd64", "go.goroot": "C:\\Users\\<USER>\\go", "go.toolsManagement.autoUpdate": true, "typescript.preferences.importModuleSpecifier": "relative", "eslint.work  ingDirectories": ["web"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "extensions": {"recommendations": ["golang.go", "vue.volar", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-next"]}}