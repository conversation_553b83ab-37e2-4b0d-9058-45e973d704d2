package invoice

import (
	"errors"

	"whlxyc.cn/server/global"
	invoiceModel "whlxyc.cn/server/model/invoice"
	"whlxyc.cn/server/model/invoice/request"
)

type InvoiceInfoService struct{}

// CreateInvoiceInfo 创建开票信息
func (s *InvoiceInfoService) CreateInvoiceInfo(req *request.CreateInvoiceInfoRequest, userID uint) (invoiceInfo *invoiceModel.InvoiceInfo, err error) {
	// 如果设置为默认，先取消其他默认设置
	if req.IsDefault {
		err = global.DY_DB.Model(&invoiceModel.InvoiceInfo{}).Where("user_id = ? AND is_default = ?", userID, true).Update("is_default", false).Error
		if err != nil {
			return nil, err
		}
	}

	invoiceInfo = &invoiceModel.InvoiceInfo{
		UserID:       userID,
		InvoiceType:  req.InvoiceType,
		InvoiceTitle: req.InvoiceTitle,
		TaxNumber:    req.TaxNumber,
		CompanyPhone: req.CompanyPhone,
		BankName:     req.BankName,
		BankAccount:  req.BankAccount,
		ContactPhone: req.ContactPhone,
		ContactEmail: req.ContactEmail,
		IsDefault:    req.IsDefault,
	}

	err = global.DY_DB.Create(invoiceInfo).Error
	return invoiceInfo, err
}

// UpdateInvoiceInfo 更新开票信息
func (s *InvoiceInfoService) UpdateInvoiceInfo(req *request.UpdateInvoiceInfoRequest, userID uint) (err error) {
	// 检查开票信息是否存在且属于当前用户
	var invoiceInfo invoiceModel.InvoiceInfo
	err = global.DY_DB.Where("id = ? AND user_id = ?", req.ID, userID).First(&invoiceInfo).Error
	if err != nil {
		return errors.New("开票信息不存在")
	}

	// 如果设置为默认，先取消其他默认设置
	if req.IsDefault {
		err = global.DY_DB.Model(&invoiceModel.InvoiceInfo{}).Where("user_id = ? AND id != ? AND is_default = ?", userID, req.ID, true).Update("is_default", false).Error
		if err != nil {
			return err
		}
	}

	// 更新开票信息
	updates := map[string]interface{}{
		"invoice_type":  req.InvoiceType,
		"invoice_title": req.InvoiceTitle,
		"tax_number":    req.TaxNumber,
		"company_phone": req.CompanyPhone,
		"bank_name":     req.BankName,
		"bank_account":  req.BankAccount,
		"contact_phone": req.ContactPhone,
		"contact_email": req.ContactEmail,
		"is_default":    req.IsDefault,
	}

	return global.DY_DB.Model(&invoiceInfo).Updates(updates).Error
}

// DeleteInvoiceInfo 删除开票信息
func (s *InvoiceInfoService) DeleteInvoiceInfo(req *request.DeleteInvoiceInfoRequest, userID uint) (err error) {
	// 检查开票信息是否存在且属于当前用户
	var invoiceInfo invoiceModel.InvoiceInfo
	err = global.DY_DB.Where("id = ? AND user_id = ?", req.ID, userID).First(&invoiceInfo).Error
	if err != nil {
		return errors.New("开票信息不存在")
	}

	// 检查是否有关联的发票
	var count int64
	err = global.DY_DB.Model(&invoiceModel.Invoice{}).Where("user_id = ?", userID).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该开票信息已有关联发票，无法删除")
	}

	return global.DY_DB.Delete(&invoiceInfo).Error
}

// GetInvoiceInfoByID 根据ID获取开票信息
func (s *InvoiceInfoService) GetInvoiceInfoByID(id uint, userID uint) (invoiceInfo *invoiceModel.InvoiceInfo, err error) {
	err = global.DY_DB.Where("id = ? AND user_id = ?", id, userID).First(&invoiceInfo).Error
	return invoiceInfo, err
}

// GetInvoiceInfoList 获取开票信息列表
func (s *InvoiceInfoService) GetInvoiceInfoList(req *request.InvoiceInfoSearch, userID uint) (total int64, invoiceInfoList []invoiceModel.InvoiceInfo, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := global.DY_DB.Model(&invoiceModel.InvoiceInfo{}).Where("user_id = ?", userID)

	// 构建查询条件
	if req.InvoiceType != "" {
		db = db.Where("invoice_type = ?", req.InvoiceType)
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取列表
	err = db.Limit(limit).Offset(offset).Order("is_default DESC, created_at DESC").Find(&invoiceInfoList).Error
	return
}

// GetDefaultInvoiceInfo 获取默认开票信息
func (s *InvoiceInfoService) GetDefaultInvoiceInfo(userID uint) (invoiceInfo *invoiceModel.InvoiceInfo, err error) {
	err = global.DY_DB.Where("user_id = ? AND is_default = ?", userID, true).First(&invoiceInfo).Error
	return invoiceInfo, err
}
