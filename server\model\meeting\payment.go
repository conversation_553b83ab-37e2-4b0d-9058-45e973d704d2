package meeting

import (
	"time"

	"gorm.io/datatypes"
	"whlxyc.cn/server/global"
)

// Payment 结构体  Payments
type Payment struct {
	global.DY_MODEL
	UserID                *uint                `json:"userId" form:"userId" gorm:"column:user_id;size:20;"`                                                                          //userId字段
	MeetingRegistrationID *uint                `json:"meetingRegistrationId" form:"meetingRegistrationId" gorm:"column:meeting_registration_id;size:20;"`                            //meetingRegistrationId字段
	PaymentMethod         *string              `json:"paymentMethod" form:"paymentMethod" gorm:"comment:支付方式;column:payment_method;size:255;"`                                       //支付方式
	TransactionId         *string              `json:"transactionId" form:"transactionId" gorm:"comment:交易ID;column:transaction_id;size:255;"`                                       //交易ID
	WechatTransactionId   *string              `json:"wechatTransactionId" form:"wechatTransactionId" gorm:"comment:微信交易号;column:wechat_transaction_id;size:255;"`                   //微信交易号
	WechatPrepayId        *string              `json:"wechatPrepayId" form:"wechatPrepayId" gorm:"comment:微信预支付ID;column:wechat_prepay_id;size:255;"`                                //微信预支付ID
	Amount                *float64             `json:"amount" form:"amount" gorm:"comment:支付金额;column:amount;size:8;"`                                                               //支付金额
	Status                string               `json:"status" form:"status" gorm:"comment:支付状态;column:status;type:enum('pending','success','failed','cancelled','refunded');"`       //支付状态
	PaymentData           datatypes.JSON       `json:"paymentData" form:"paymentData" gorm:"comment:支付平台返回数据;column:payment_data;" swaggertype:"object"`                             //支付平台返回数据
	WechatCallbackData    *string              `json:"wechatCallbackData" form:"wechatCallbackData" gorm:"comment:微信回调数据;column:wechat_callback_data;"`                              //微信回调数据
	TransferScreenshot    *string              `json:"transferScreenshot" form:"transferScreenshot" gorm:"comment:转账截图路径;column:transfer_screenshot;size:255;"`                      //转账截图路径
	ReviewStatus          string               `json:"reviewStatus" form:"reviewStatus" gorm:"comment:审核状态;column:review_status;type:enum('pending_review','approved','rejected');"` //审核状态
	ReviewNotes           *string              `json:"reviewNotes" form:"reviewNotes" gorm:"comment:审核备注;column:review_notes;"`                                                      //审核备注
	PaidAt                *time.Time           `json:"paidAt" form:"paidAt" gorm:"comment:支付时间;column:paid_at;"`                                                                     //支付时间
	WechatPaidAt          *time.Time           `json:"wechatPaidAt" form:"wechatPaidAt" gorm:"comment:微信支付时间;column:wechat_paid_at;"`                                                //微信支付时间
	FailureReason         *string              `json:"failureReason" form:"failureReason" gorm:"comment:失败原因;column:failure_reason;"`                                                //失败原因
	ReviewedBy            *int                 `json:"reviewedBy" form:"reviewedBy" gorm:"column:reviewed_by;size:20;"`                                                              //reviewedBy字段
	ReviewedAt            *time.Time           `json:"reviewedAt" form:"reviewedAt" gorm:"comment:审核时间;column:reviewed_at;"`
	User                  *User                `json:"user" gorm:"foreignKey:UserID"`
	MeetingRegistration   *MeetingRegistration `json:"meetingRegistration" gorm:"foreignKey:MeetingRegistrationID"`
	//审核时间
}

// TableName payments表 Payments自定义表名 payments
func (Payment) TableName() string {
	return "payments"
}

// 支付方式常量
const (
	PaymentMethodAlipay = "alipay" // 支付宝
	PaymentMethodWechat = "wechat" // 微信支付
	PaymentMethodBank   = "bank"   // 银行转账
	PaymentMethodCash   = "cash"   // 现金支付
)

// 支付状态常量
const (
	PaymentStatusPending   = "pending"   // 待支付
	PaymentStatusSuccess   = "success"   // 支付成功
	PaymentStatusFailed    = "failed"    // 支付失败
	PaymentStatusCancelled = "cancelled" // 已取消
	PaymentStatusRefunded  = "refunded"  // 已退款
)

// 审核状态常量
const (
	ReviewStatusPendingReview = "pending_review" // 待审核
	ReviewStatusApproved      = "approved"       // 已通过
	ReviewStatusRejected      = "rejected"       // 已拒绝
)
