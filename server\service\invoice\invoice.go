package invoice

import (
	"errors"
	"fmt"
	"time"

	"whlxyc.cn/server/global"
	invoiceModel "whlxyc.cn/server/model/invoice"
	"whlxyc.cn/server/model/invoice/request"
	"whlxyc.cn/server/model/invoice/response"
)

type InvoiceService struct{}

// ApplyInvoice 申请开票
func (s *InvoiceService) ApplyInvoice(req *request.ApplyInvoiceRequest, userID uint) (invoice *invoiceModel.Invoice, err error) {
	// 检查支付记录是否存在且属于当前用户
	var paymentExists bool
	err = global.DY_DB.Raw("SELECT EXISTS(SELECT 1 FROM payments WHERE id = ? AND user_id = ? AND status = 'success')", req.PaymentID, userID).Scan(&paymentExists).Error
	if err != nil {
		return nil, err
	}
	if !paymentExists {
		return nil, errors.New("支付记录不存在或支付未成功")
	}

	// 检查是否已经申请过发票
	var existingInvoice invoiceModel.Invoice
	err = global.DY_DB.Where("payment_id = ? AND user_id = ?", req.PaymentID, userID).First(&existingInvoice).Error
	if err == nil {
		return nil, errors.New("该支付记录已申请过发票")
	}

	// 检查开票信息是否存在且属于当前用户
	var invoiceInfo invoiceModel.InvoiceInfo
	err = global.DY_DB.Where("id = ? AND user_id = ?", req.InvoiceInfoID, userID).First(&invoiceInfo).Error
	if err != nil {
		return nil, errors.New("开票信息不存在")
	}

	// 创建发票申请
	invoice = &invoiceModel.Invoice{
		UserID:       userID,
		PaymentID:    req.PaymentID,
		InvoiceTitle: req.InvoiceTitle,
		TaxNumber:    req.TaxNumber,
		Amount:       req.Amount,
		Status:       invoiceModel.InvoiceStatusPending,
		Notes:        req.Notes,
	}

	err = global.DY_DB.Create(invoice).Error
	return invoice, err
}

// UpdateInvoiceStatus 更新发票状态
func (s *InvoiceService) UpdateInvoiceStatus(req *request.UpdateInvoiceStatusRequest) (err error) {
	// 检查发票是否存在
	var invoice invoiceModel.Invoice
	err = global.DY_DB.First(&invoice, req.ID).Error
	if err != nil {
		return errors.New("发票不存在")
	}

	// 构建更新数据
	updates := map[string]interface{}{
		"status": req.Status,
		"notes":  req.Notes,
	}

	// 如果是已开票状态，需要设置发票号码和开票日期
	if req.Status == invoiceModel.InvoiceStatusIssued {
		if req.InvoiceNumber == "" {
			return errors.New("已开票状态必须提供发票号码")
		}
		updates["invoice_number"] = req.InvoiceNumber
		if req.IssueDate != nil {
			updates["issue_date"] = req.IssueDate
		} else {
			now := time.Now()
			updates["issue_date"] = &now
		}
		if req.FilePath != "" {
			updates["file_path"] = req.FilePath
		}
	}

	return global.DY_DB.Model(&invoice).Updates(updates).Error
}

// GetInvoiceByID 根据ID获取发票
func (s *InvoiceService) GetInvoiceByID(id uint) (invoice *invoiceModel.Invoice, err error) {
	db := global.DY_DB.Preload("User")
	db = db.Where("id = ?", id)
	err = db.First(&invoice).Error
	return invoice, err
}

// GetInvoiceList 获取发票列表
func (s *InvoiceService) GetInvoiceList(req *request.InvoiceSearch) (total int64, invoiceList []invoiceModel.Invoice, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := global.DY_DB.Model(&invoiceModel.Invoice{}).Preload("User")

	// 构建查询条件

	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.StartDate != "" {
		db = db.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		db = db.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取列表
	err = db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&invoiceList).Error
	return
}

// DeleteInvoice 删除发票
func (s *InvoiceService) DeleteInvoice(req *request.DeleteInvoiceRequest, userID *uint) (err error) {
	// 检查发票是否存在
	var invoice invoiceModel.Invoice
	db := global.DY_DB
	if userID != nil {
		db = db.Where("id = ? AND user_id = ?", req.ID, *userID)
	} else {
		db = db.Where("id = ?", req.ID)
	}
	err = db.First(&invoice).Error
	if err != nil {
		return errors.New("发票不存在")
	}

	// 只有待处理和失败状态的发票可以删除
	if invoice.Status != invoiceModel.InvoiceStatusPending && invoice.Status != invoiceModel.InvoiceStatusFailed {
		return errors.New("只有待处理和失败状态的发票可以删除")
	}

	return global.DY_DB.Delete(&invoice).Error
}

// GetInvoiceStatistics 获取发票统计
func (s *InvoiceService) GetInvoiceStatistics(userID *uint) (stats *response.InvoiceStatisticsResponse, err error) {
	stats = &response.InvoiceStatisticsResponse{}

	db := global.DY_DB.Model(&invoiceModel.Invoice{})
	if userID != nil {
		db = db.Where("user_id = ?", *userID)
	}

	// 总发票数
	err = db.Count(&stats.TotalInvoices).Error
	if err != nil {
		return nil, err
	}

	// 各状态发票数
	err = db.Where("status = ?", invoiceModel.InvoiceStatusPending).Count(&stats.PendingInvoices).Error
	if err != nil {
		return nil, err
	}

	err = db.Where("status = ?", invoiceModel.InvoiceStatusIssued).Count(&stats.IssuedInvoices).Error
	if err != nil {
		return nil, err
	}

	err = db.Where("status = ?", invoiceModel.InvoiceStatusFailed).Count(&stats.FailedInvoices).Error
	if err != nil {
		return nil, err
	}

	// 总金额
	err = db.Select("COALESCE(SUM(amount), 0)").Scan(&stats.TotalAmount).Error
	if err != nil {
		return nil, err
	}

	// 已开票金额
	err = db.Where("status = ?", invoiceModel.InvoiceStatusIssued).Select("COALESCE(SUM(amount), 0)").Scan(&stats.IssuedAmount).Error
	if err != nil {
		return nil, err
	}

	return stats, nil
}

// GenerateInvoiceNumber 生成发票号码
func (s *InvoiceService) GenerateInvoiceNumber() string {
	now := time.Now()
	return fmt.Sprintf("INV%s%06d", now.Format("20060102"), now.Unix()%1000000)
}
