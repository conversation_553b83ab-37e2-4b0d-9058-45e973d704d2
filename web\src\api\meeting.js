import service from '@/utils/request'

// @Summary 创建会议
// @Produce application/json
// @Param data body CreateMeetingRequest true "创建会议"
// @Router /meeting/createMeeting [post]
export const createMeeting = (data) => {
  return service({
    url: '/meeting/createMeeting',
    method: 'post',
    data: data
  })
}

// @Summary 更新会议
// @Produce application/json
// @Param data body UpdateMeetingRequest true "更新会议"
// @Router /meeting/updateMeeting [put]
export const updateMeeting = (data) => {
  return service({
    url: '/meeting/updateMeeting',
    method: 'put',
    data: data
  })
}

// @Summary 删除会议
// @Produce application/json
// @Param data body DeleteMeetingRequest true "删除会议"
// @Router /meeting/deleteMeeting [delete]
export const deleteMeeting = (data) => {
  return service({
    url: '/meeting/deleteMeeting',
    method: 'delete',
    data: data
  })
}

// @Summary 根据ID获取会议详情
// @Produce application/json
// @Param id query uint true "会议ID"
// @Router /meeting/findMeeting [get]
export const getMeetingById = (id) => {
  return service({
    url: '/meeting/findMeeting',
    method: 'get',
    params: { id }
  })
}

// @Summary 获取会议列表
// @Produce application/json
// @Param data query MeetingSearch true "分页获取会议列表"
// @Router /meeting/getMeetingList [get]
export const getMeetingList = (params) => {
  return service({
    url: '/meeting/getMeetingList',
    method: 'get',
    params: params
  })
}

// @Summary 更新参与者状态
// @Produce application/json
// @Param data body UpdateParticipantStatusRequest true "更新参与者状态"
// @Router /meeting/updateParticipantStatus [put]
export const updateParticipantStatus = (data) => {
  return service({
    url: '/meeting/updateParticipantStatus',
    method: 'put',
    data: data
  })
}

// @Summary 获取用户会议列表
// @Produce application/json
// @Param data query UserMeetingRequest true "获取用户会议列表"
// @Router /meeting/getUserMeetings [get]
export const getUserMeetings = (params) => {
  return service({
    url: '/meeting/getUserMeetings',
    method: 'get',
    params: params
  })
}

// @Summary 获取会议统计信息
// @Produce application/json
// @Router /meeting/getMeetingStatistics [get]
export const getMeetingStatistics = () => {
  return service({
    url: '/meeting/getMeetingStatistics',
    method: 'get'
  })
}

// 会议状态常量
export const MEETING_STATUS = {
  DRAFT: 'draft',         // 草稿
  PUBLISHED: 'published', // 已发布
  COMPLETED: 'completed', // 已完成
  CANCELLED: 'cancelled'  // 已取消
}

// 会议类型常量
export const MEETING_TYPE = {
  ONLINE: 'online',       // 线上
  OFFLINE: 'offline',     // 线下
  HYBRID: 'hybrid'        // 混合
}

// 注册类型常量
export const REGISTRATION_TYPE = {
  EXPERT: 'expert',       // 专家
  STUDENT: 'student',     // 学生
  CORPORATE: 'corporate'  // 企业代表
}

// 支付状态常量
export const PAYMENT_STATUS = {
  PENDING: 'pending',     // 待支付
  PAID: 'paid',          // 已支付
  FAILED: 'failed',      // 支付失败
  REFUNDED: 'refunded'   // 已退款
}

// 状态显示文本映射
export const MEETING_STATUS_TEXT = {
  [MEETING_STATUS.DRAFT]: '草稿',
  [MEETING_STATUS.PUBLISHED]: '已发布',
  [MEETING_STATUS.COMPLETED]: '已完成',
  [MEETING_STATUS.CANCELLED]: '已取消'
}

// 会议类型显示文本映射
export const MEETING_TYPE_TEXT = {
  [MEETING_TYPE.ONLINE]: '线上会议',
  [MEETING_TYPE.OFFLINE]: '线下会议',
  [MEETING_TYPE.HYBRID]: '混合会议'
}

// 注册类型显示文本映射
export const REGISTRATION_TYPE_TEXT = {
  [REGISTRATION_TYPE.EXPERT]: '专家',
  [REGISTRATION_TYPE.STUDENT]: '学生',
  [REGISTRATION_TYPE.CORPORATE]: '企业代表'
}

// 支付状态显示文本映射
export const PAYMENT_STATUS_TEXT = {
  [PAYMENT_STATUS.PENDING]: '待支付',
  [PAYMENT_STATUS.PAID]: '已支付',
  [PAYMENT_STATUS.FAILED]: '支付失败',
  [PAYMENT_STATUS.REFUNDED]: '已退款'
}

// 状态颜色映射
export const MEETING_STATUS_COLOR = {
  [MEETING_STATUS.DRAFT]: 'info',
  [MEETING_STATUS.PUBLISHED]: 'success',
  [MEETING_STATUS.COMPLETED]: 'primary',
  [MEETING_STATUS.CANCELLED]: 'danger'
}

// 注册类型颜色映射
export const REGISTRATION_TYPE_COLOR = {
  [REGISTRATION_TYPE.EXPERT]: 'success',
  [REGISTRATION_TYPE.STUDENT]: 'primary',
  [REGISTRATION_TYPE.CORPORATE]: 'warning'
}

// 支付状态颜色映射
export const PAYMENT_STATUS_COLOR = {
  [PAYMENT_STATUS.PENDING]: 'warning',
  [PAYMENT_STATUS.PAID]: 'success',
  [PAYMENT_STATUS.FAILED]: 'danger',
  [PAYMENT_STATUS.REFUNDED]: 'info'
}
