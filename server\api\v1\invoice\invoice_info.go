package invoice

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"whlxyc.cn/server/global"
	"whlxyc.cn/server/model/common/response"
	"whlxyc.cn/server/model/invoice/request"
	"whlxyc.cn/server/utils"
)

type InvoiceInfoApi struct{}

// CreateInvoiceInfo 创建开票信息
// @Tags InvoiceInfo
// @Summary 创建开票信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CreateInvoiceInfoRequest true "创建开票信息"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /invoice/createInvoiceInfo [post]
func (i *InvoiceInfoApi) CreateInvoiceInfo(c *gin.Context) {
	var req request.CreateInvoiceInfoRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	invoiceInfo, err := invoiceInfoService.CreateInvoiceInfo(&req, userID)
	if err != nil {
		global.DY_LOG.Error("创建开票信息失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithData(invoiceInfo, c)
}

// UpdateInvoiceInfo 更新开票信息
// @Tags InvoiceInfo
// @Summary 更新开票信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateInvoiceInfoRequest true "更新开票信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /invoice/updateInvoiceInfo [put]
func (i *InvoiceInfoApi) UpdateInvoiceInfo(c *gin.Context) {
	var req request.UpdateInvoiceInfoRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = invoiceInfoService.UpdateInvoiceInfo(&req, userID)
	if err != nil {
		global.DY_LOG.Error("更新开票信息失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteInvoiceInfo 删除开票信息
// @Tags InvoiceInfo
// @Summary 删除开票信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.DeleteInvoiceInfoRequest true "删除开票信息"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /invoice/deleteInvoiceInfo [delete]
func (i *InvoiceInfoApi) DeleteInvoiceInfo(c *gin.Context) {
	var req request.DeleteInvoiceInfoRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = invoiceInfoService.DeleteInvoiceInfo(&req, userID)
	if err != nil {
		global.DY_LOG.Error("删除开票信息失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetInvoiceInfoByID 根据ID获取开票信息
// @Tags InvoiceInfo
// @Summary 根据ID获取开票信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "开票信息ID"
// @Success 200 {object} response.Response{data=invoice.InvoiceInfo} "获取成功"
// @Router /invoice/findInvoiceInfo [get]
func (i *InvoiceInfoApi) GetInvoiceInfoByID(c *gin.Context) {
	var req struct {
		ID uint `form:"id" binding:"required"`
	}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	invoiceInfo, err := invoiceInfoService.GetInvoiceInfoByID(req.ID, userID)
	if err != nil {
		global.DY_LOG.Error("查询开票信息失败!", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	response.OkWithData(invoiceInfo, c)
}

// GetInvoiceInfoList 获取开票信息列表
// @Tags InvoiceInfo
// @Summary 获取开票信息列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.InvoiceInfoSearch true "获取开票信息列表"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /invoice/getInvoiceInfoList [get]
func (i *InvoiceInfoApi) GetInvoiceInfoList(c *gin.Context) {
	var req request.InvoiceInfoSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	total, invoiceInfoList, err := invoiceInfoService.GetInvoiceInfoList(&req, userID)
	if err != nil {
		global.DY_LOG.Error("获取开票信息列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(response.PageResult{
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     invoiceInfoList,
		Total:    total,
	}, c)
}

// GetDefaultInvoiceInfo 获取默认开票信息
// @Tags InvoiceInfo
// @Summary 获取默认开票信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=invoice.InvoiceInfo} "获取成功"
// @Router /invoice/getDefaultInvoiceInfo [get]
func (i *InvoiceInfoApi) GetDefaultInvoiceInfo(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)

	invoiceInfo, err := invoiceInfoService.GetDefaultInvoiceInfo(userID)
	if err != nil {
		global.DY_LOG.Error("获取默认开票信息失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(invoiceInfo, c)
}
