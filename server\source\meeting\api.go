package meeting

import (
	"context"

	"github.com/pkg/errors"
	"gorm.io/gorm"
	sysModel "whlxyc.cn/server/model/system"
	"whlxyc.cn/server/service/system"
)

type initApi struct{}

const initOrderApi = system.InitOrderSystem + 1

// auto run
func init() {
	system.RegisterInit(initOrderApi, &initApi{})
}

func (i *initApi) InitializerName() string {
	return "meeting_apis"
}

func (i *initApi) MigrateTable(ctx context.Context) (context.Context, error) {
	return ctx, nil // API表已存在，无需迁移
}

func (i *initApi) TableCreated(ctx context.Context) bool {
	return true // API表已存在
}

func (i *initApi) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	entities := []sysModel.SysApi{
		{ApiGroup: "会议管理", Method: "POST", Path: "/meeting/createMeeting", Description: "创建会议"},
		{ApiGroup: "会议管理", Method: "PUT", Path: "/meeting/updateMeeting", Description: "更新会议"},
		{ApiGroup: "会议管理", Method: "DELETE", Path: "/meeting/deleteMeeting", Description: "删除会议"},
		{ApiGroup: "会议管理", Method: "GET", Path: "/meeting/findMeeting", Description: "根据ID获取会议详情"},
		{ApiGroup: "会议管理", Method: "GET", Path: "/meeting/getMeetingList", Description: "获取会议列表"},
		{ApiGroup: "会议管理", Method: "PUT", Path: "/meeting/updateParticipantStatus", Description: "更新注册支付状态"},
		{ApiGroup: "会议管理", Method: "GET", Path: "/meeting/getUserMeetings", Description: "获取用户会议列表"},
		{ApiGroup: "会议管理", Method: "GET", Path: "/meeting/getMeetingStatistics", Description: "获取会议统计信息"},
	}

	if err := db.Create(&entities).Error; err != nil {
		return ctx, errors.Wrap(err, "会议管理API数据初始化失败!")
	}
	next := context.WithValue(ctx, i.InitializerName(), entities)
	return next, nil
}

func (i *initApi) DataInserted(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	if errors.Is(db.Where("path = ? AND method = ?", "/meeting/createMeeting", "POST").
		First(&sysModel.SysApi{}).Error, gorm.ErrRecordNotFound) { // 判断是否存在数据
		return false
	}
	return true
}
