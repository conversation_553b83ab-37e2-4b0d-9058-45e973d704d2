<template>
  <el-dialog
    v-model="visible"
    title="申请开票"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="100px">
      <el-form-item label="会议信息:">
        <div class="meeting-info">
          <div class="meeting-title">{{ meetingInfo.title }}</div>
          <div class="meeting-details">
            <span>费用: ¥{{ meetingInfo.amount }}</span>
            <span class="ml-4">支付状态:
              <el-tag type="success">已支付</el-tag>
            </span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="支付记录:" prop="paymentId">
        <el-select
          v-model="formData.paymentId"
          placeholder="请选择支付记录"
          style="width: 100%"
          @change="onPaymentChange"
        >
          <el-option
            v-for="payment in availablePayments"
            :key="payment.id"
            :label="`${payment.meetingTitle} - ¥${payment.amount}`"
            :value="payment.id"
          />
        </el-select>
        <div class="mt-2 text-sm text-gray-500">
          选择支付记录后将自动填充发票金额
        </div>
      </el-form-item>
      
      <el-form-item label="开票信息:" prop="invoiceInfoId">
        <el-select 
          v-model="formData.invoiceInfoId" 
          placeholder="请选择开票信息" 
          style="width: 100%"
          @change="onInvoiceInfoChange"
        >
          <el-option
            v-for="info in invoiceInfoList"
            :key="info.ID"
            :label="`${info.invoiceTitle} (${INVOICE_TYPE_TEXT[info.invoiceType]})`"
            :value="info.ID"
          />
        </el-select>
        <div class="mt-2">
          <el-button type="text" size="small" @click="openInvoiceInfoDialog">
            <el-icon><Plus /></el-icon>
            新增开票信息
          </el-button>
          <el-button type="text" size="small" @click="refreshInvoiceInfo">
            <el-icon><Refresh /></el-icon>
            刷新列表
          </el-button>
        </div>
        <div class="mt-1 text-sm text-gray-500">
          选择开票信息后将自动填充发票抬头和税号
        </div>
      </el-form-item>
      
      <el-form-item label="发票抬头:" prop="invoiceTitle">
        <el-input v-model="formData.invoiceTitle" placeholder="请输入发票抬头" />
      </el-form-item>
      
      <el-form-item label="税号:" prop="taxNumber">
        <el-input v-model="formData.taxNumber" placeholder="请输入税号（企业发票必填）" />
      </el-form-item>
      
      <el-form-item label="发票金额1:" prop="amount">
        <el-input-number 
          v-model="formData.amount" 
          :precision="2" 
          :min="0" 
          :max="meetingInfo.amount"
          style="width: 100%" 
        />
        <div class="text-sm text-gray-500 mt-1">
          最大金额: ¥{{ meetingInfo.amount }}
        </div>
      </el-form-item>
      
      <el-form-item label="发票文件:" prop="filePath">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          :action="uploadAction"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :file-list="fileList"
          :limit="1"
          accept=".pdf,.jpg,.jpeg,.png"
        >
          <el-button type="primary">
            <el-icon><Upload /></el-icon>
            上传发票文件
          </el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持 PDF、JPG、PNG 格式，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
        <div v-if="formData.filePath" class="mt-2">
          <el-tag type="success" closable @close="removeFile">
            <el-icon><Document /></el-icon>
            {{ getFileName(formData.filePath) }}
          </el-tag>
        </div>
      </el-form-item>

      <el-form-item label="备注:">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
      </span>
    </template>
    
    <!-- 新增开票信息弹窗 -->
    <InvoiceInfoForm
      v-model:visible="invoiceInfoDialogVisible"
      @success="onInvoiceInfoSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  applyInvoice,
  getInvoiceInfoList,
  INVOICE_TYPE_TEXT
} from '@/api/invoice'
import { getAvailablePaymentsForInvoice } from '@/api/payment'
import InvoiceInfoForm from './InvoiceInfoForm.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  meetingInfo: {
    type: Object,
    default: () => ({
      id: null,
      title: '',
      amount: 0,
      paymentId: null
    })
  }
})

const emit = defineEmits(['update:visible', 'success'])

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const formRef = ref()
const uploadRef = ref()
const loading = ref(false)
const invoiceInfoList = ref([])
const invoiceInfoDialogVisible = ref(false)
const availablePayments = ref([])
const fileList = ref([])

const formData = ref({
  paymentId: null,
  invoiceInfoId: null,
  invoiceTitle: '',
  taxNumber: '',
  amount: 0,
  filePath: '',
  notes: ''
})

const rules = reactive({
  invoiceInfoId: [{
    required: true,
    message: '请选择开票信息',
    trigger: 'change'
  }],
  invoiceTitle: [{
    required: true,
    message: '请输入发票抬头',
    trigger: 'blur'
  }],
  amount: [{
    required: true,
    message: '请输入发票金额',
    trigger: 'blur'
  }, {
    type: 'number',
    min: 0.01,
    message: '发票金额必须大于0',
    trigger: 'blur'
  }]
})

// 上传相关配置
const uploadAction = computed(() => {
  return `${import.meta.env.VITE_BASE_API}/fileUploadAndDownload/upload`
})

const uploadHeaders = computed(() => {
  return {
    'x-token': localStorage.getItem('token') || ''
  }
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
    loadInvoiceInfoList()
    loadAvailablePayments()
  }
})

// 初始化表单
const initForm = () => {
  formData.value = {
    paymentId: props.meetingInfo.paymentId,
    invoiceInfoId: null,
    invoiceTitle: '',
    taxNumber: '',
    amount: props.meetingInfo.amount,
    filePath: '',
    notes: ''
  }
  fileList.value = []
}

// 加载开票信息列表
const loadInvoiceInfoList = async () => {
  try {
    const res = await getInvoiceInfoList({ page: 1, pageSize: 100 })
    if (res.code === 0) {
      invoiceInfoList.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载开票信息失败:', error)
  }
}

// 加载可用的支付记录
const loadAvailablePayments = async () => {
  try {
    // 调用实际的API获取用户的已支付但未开票的支付记录
    const paymentsRes = await getAvailablePaymentsForInvoice()
    if (paymentsRes.code === 0) {
      availablePayments.value = paymentsRes.data.map(payment => ({
        id: payment.ID,
        meetingTitle: payment.meeting?.title || '未知会议',
        amount: payment.amount,
        orderNumber: payment.orderNumber
      }))
    }
  } catch (error) {
    console.error('获取可开票支付记录失败:', error)
    // 如果API失败，使用模拟数据
    availablePayments.value = [
      {
        id: props.meetingInfo.paymentId || 1,
        meetingTitle: props.meetingInfo.title,
        amount: props.meetingInfo.amount
      }
    ]
  }

  // 如果有默认支付记录，自动选择
  if (availablePayments.value.length > 0) {
    formData.value.paymentId = availablePayments.value[0].id
    formData.value.amount = availablePayments.value[0].amount
  }
}

// 开票信息变更时自动填充
const onInvoiceInfoChange = (invoiceInfoId) => {
  const selectedInfo = invoiceInfoList.value.find(info => info.ID === invoiceInfoId)
  if (selectedInfo) {
    formData.value.invoiceTitle = selectedInfo.invoiceTitle
    formData.value.taxNumber = selectedInfo.taxNumber || ''
  }
}

// 支付记录变更时自动填充金额
const onPaymentChange = (paymentId) => {
  const selectedPayment = availablePayments.value.find(payment => payment.id === paymentId)
  if (selectedPayment) {
    formData.value.amount = selectedPayment.amount
  }
}

// 打开新增开票信息弹窗
const openInvoiceInfoDialog = () => {
  invoiceInfoDialogVisible.value = true
}

// 刷新开票信息列表
const refreshInvoiceInfo = () => {
  loadInvoiceInfoList()
}

// 新增开票信息成功回调
const onInvoiceInfoSuccess = (newInvoiceInfo) => {
  loadInvoiceInfoList()
  // 自动选择新创建的开票信息
  setTimeout(() => {
    formData.value.invoiceInfoId = newInvoiceInfo.ID
    onInvoiceInfoChange(newInvoiceInfo.ID)
  }, 100)
}

// 文件上传前检查
const beforeUpload = (file) => {
  const isValidType = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 PDF、JPG、PNG 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 文件上传成功
const handleUploadSuccess = (response, file) => {
  if (response.code === 0) {
    formData.value.filePath = response.data.file.url
    fileList.value = [{ name: file.name, url: response.data.file.url }]
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.msg || '文件上传失败')
  }
}

// 文件上传失败
const handleUploadError = (error) => {
  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败')
}

// 移除文件
const removeFile = () => {
  formData.value.filePath = ''
  fileList.value = []
}

// 获取文件名
const getFileName = (filePath) => {
  if (!filePath) return ''
  return filePath.split('/').pop() || filePath.split('\\').pop() || '发票文件'
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  fileList.value = []
}

// 提交申请
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return
    
    loading.value = true
    const res = await applyInvoice(formData.value)
    
    if (res.code === 0) {
      ElMessage.success('申请开票成功')
      emit('success', res.data)
      handleClose()
    }
  } catch (error) {
    console.error('申请开票失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.meeting-info {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.meeting-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.meeting-details {
  font-size: 14px;
  color: #6c757d;
}

.meeting-details span {
  display: inline-block;
}
</style>
