package request

import (
	"time"

	"whlxyc.cn/server/model/common/request"
)

// CreatePaymentRequest 创建支付记录请求
type CreatePaymentRequest struct {
	MeetingRegistrationID uint    `json:"meetingRegistrationId" binding:"required"` // 会议注册ID
	Amount                float64 `json:"amount" binding:"required,gt=0"`           // 支付金额
	PaymentMethod         string  `json:"paymentMethod" binding:"required"`         // 支付方式
	TransactionID         string  `json:"transactionId" binding:"required"`         // 交易ID
	WechatTransactionID   string  `json:"wechatTransactionId"`                      // 微信交易号
	WechatPrepayID        string  `json:"wechatPrepayId"`                           // 微信预支付ID
	TransferScreenshot    string  `json:"transferScreenshot"`                       // 转账截图路径
}

// UpdatePaymentRequest 更新支付记录请求
type UpdatePaymentRequest struct {
	ID                  uint       `json:"id" binding:"required"` // 支付记录ID
	Status              string     `json:"status"`                // 支付状态
	WechatTransactionID string     `json:"wechatTransactionId"`   // 微信交易号
	WechatPrepayID      string     `json:"wechatPrepayId"`        // 微信预支付ID
	PaymentData         string     `json:"paymentData"`           // 支付平台返回数据
	WechatCallbackData  string     `json:"wechatCallbackData"`    // 微信回调数据
	TransferScreenshot  string     `json:"transferScreenshot"`    // 转账截图路径
	ReviewStatus        string     `json:"reviewStatus"`          // 审核状态
	ReviewNotes         string     `json:"reviewNotes"`           // 审核备注
	PaidAt              *time.Time `json:"paidAt"`                // 支付时间
	WechatPaidAt        *time.Time `json:"wechatPaidAt"`          // 微信支付时间
	FailureReason       string     `json:"failureReason"`         // 失败原因
}

// DeletePaymentRequest 删除支付记录请求
type DeletePaymentRequest struct {
	ID uint `json:"id" binding:"required"` // 支付记录ID
}

// PaymentSearch 支付记录搜索请求
type PaymentSearch struct {
	request.PageInfo
	UserID                *uint   `json:"userId"`                // 用户ID
	MeetingRegistrationID *uint   `json:"meetingRegistrationId"` // 会议注册ID
	Status                string  `json:"status"`                // 支付状态
	PaymentMethod         string  `json:"paymentMethod"`         // 支付方式
	ReviewStatus          string  `json:"reviewStatus"`          // 审核状态
	StartDate             string  `json:"startDate"`             // 开始日期
	EndDate               string  `json:"endDate"`               // 结束日期
	TransactionID         string  `json:"transactionId"`         // 交易ID
	MinAmount             float64 `json:"minAmount"`             // 最小金额
	MaxAmount             float64 `json:"maxAmount"`             // 最大金额
}

// RefundPaymentRequest 退款请求
type RefundPaymentRequest struct {
	ID           uint    `json:"id" binding:"required"`           // 支付记录ID
	RefundAmount float64 `json:"refundAmount" binding:"required"` // 退款金额
	RefundReason string  `json:"refundReason"`                    // 退款原因
}

// PaymentStatisticsRequest 支付统计请求
type PaymentStatisticsRequest struct {
	StartDate             string `json:"startDate"`             // 开始日期
	EndDate               string `json:"endDate"`               // 结束日期
	MeetingRegistrationID *uint  `json:"meetingRegistrationId"` // 会议注册ID
}

// ReviewPaymentRequest 审核支付记录请求
type ReviewPaymentRequest struct {
	ID           uint   `json:"id" binding:"required"`           // 支付记录ID
	ReviewStatus string `json:"reviewStatus" binding:"required"` // 审核状态
	ReviewNotes  string `json:"reviewNotes"`                     // 审核备注
}
