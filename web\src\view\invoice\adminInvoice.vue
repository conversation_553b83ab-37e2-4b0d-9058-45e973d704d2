<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="发票状态" prop="status">
          <el-select v-model="searchInfo.status" clearable placeholder="请选择发票状态">
            <el-option
              v-for="(value, key) in INVOICE_STATUS_TEXT"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model.number="searchInfo.userId" clearable placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="searchInfo.startDate"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="searchInfo.endDate"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 统计卡片 -->
    <div class="gva-card-box mb-4">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-content">
              <div class="statistics-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-number">{{ statistics.pendingInvoices }}</div>
                <div class="statistics-label">待处理</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-content">
              <div class="statistics-icon processing">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-number">{{ statistics.totalInvoices - statistics.pendingInvoices - statistics.issuedInvoices - statistics.failedInvoices }}</div>
                <div class="statistics-label">处理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-content">
              <div class="statistics-icon issued">
                <el-icon><Check /></el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-number">{{ statistics.issuedInvoices }}</div>
                <div class="statistics-label">已开票</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-content">
              <div class="statistics-icon failed">
                <el-icon><Close /></el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-number">{{ statistics.failedInvoices }}</div>
                <div class="statistics-label">开票失败</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
      >
        <el-table-column align="left" label="申请日期" prop="CreatedAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="left" label="用户" prop="user" width="120">
          <template #default="scope">
            <div v-if="scope.row.user">
              <div>{{ scope.row.user.nickName }}</div>
              <div class="text-xs text-gray-500">ID: {{ scope.row.user.ID }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="发票号码" prop="invoiceNumber" width="180">
          <template #default="scope">
            <span v-if="scope.row.invoiceNumber">{{ scope.row.invoiceNumber }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="发票抬头" prop="invoiceTitle" min-width="200" />
        <el-table-column align="left" label="发票金额" prop="amount" width="120">
          <template #default="scope">
            <span class="text-red-500 font-medium">¥{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="发票状态" prop="status" width="120">
          <template #default="scope">
            <el-tag :type="INVOICE_STATUS_COLOR[scope.row.status]" :icon="INVOICE_STATUS_ICON[scope.row.status]">
              {{ INVOICE_STATUS_TEXT[scope.row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="开票日期" prop="issueDate" width="180">
          <template #default="scope">
            <span v-if="scope.row.issueDate">{{ formatDate(scope.row.issueDate) }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" min-width="240">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
              <el-icon style="margin-right: 5px"><InfoFilled /></el-icon>
              查看详情
            </el-button>
            <el-button type="warning" link class="table-button" @click="updateStatus(scope.row)">
              <el-icon style="margin-right: 5px"><Edit /></el-icon>
              更新状态
            </el-button>
            <el-button 
              v-if="scope.row.status === INVOICE_STATUS.ISSUED && scope.row.filePath" 
              type="success" 
              link 
              class="table-button" 
              @click="downloadInvoice(scope.row)"
            >
              <el-icon style="margin-right: 5px"><Download /></el-icon>
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 更新状态弹窗 -->
    <el-dialog
      v-model="statusDialogVisible"
      title="更新发票状态"
      width="600px"
      :before-close="closeStatusDialog"
    >
      <el-form :model="statusFormData" ref="statusFormRef" :rules="statusRule" label-width="100px">
        <el-form-item label="发票状态:" prop="status">
          <el-radio-group v-model="statusFormData.status">
            <el-radio :value="INVOICE_STATUS.PENDING">{{ INVOICE_STATUS_TEXT[INVOICE_STATUS.PENDING] }}</el-radio>
            <el-radio :value="INVOICE_STATUS.PROCESSING">{{ INVOICE_STATUS_TEXT[INVOICE_STATUS.PROCESSING] }}</el-radio>
            <el-radio :value="INVOICE_STATUS.ISSUED">{{ INVOICE_STATUS_TEXT[INVOICE_STATUS.ISSUED] }}</el-radio>
            <el-radio :value="INVOICE_STATUS.FAILED">{{ INVOICE_STATUS_TEXT[INVOICE_STATUS.FAILED] }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="statusFormData.status === INVOICE_STATUS.ISSUED" label="发票号码:" prop="invoiceNumber">
          <el-input v-model="statusFormData.invoiceNumber" placeholder="请输入发票号码" />
        </el-form-item>
        <el-form-item v-if="statusFormData.status === INVOICE_STATUS.ISSUED" label="开票日期:" prop="issueDate">
          <el-date-picker
            v-model="statusFormData.issueDate"
            type="datetime"
            placeholder="选择开票日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item v-if="statusFormData.status === INVOICE_STATUS.ISSUED" label="发票文件:" prop="filePath">
          <el-input v-model="statusFormData.filePath" placeholder="请输入发票文件路径" />
          <div class="mt-2 text-sm text-gray-500">
            提示：这里应该集成文件上传组件
          </div>
        </el-form-item>
        <el-form-item label="备注:" prop="notes">
          <el-input v-model="statusFormData.notes" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeStatusDialog">取 消</el-button>
          <el-button type="primary" @click="submitStatusUpdate">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 发票详情弹窗 -->
    <InvoiceDetail
      v-model:visible="detailDialogVisible"
      :invoice-id="selectedInvoiceId"
    />
  </div>
</template>

<script setup>
import {
  getAdminInvoiceList,
  getAdminInvoiceStatistics,
  updateInvoiceStatus,
  INVOICE_STATUS,
  INVOICE_STATUS_TEXT,
  INVOICE_STATUS_COLOR,
  INVOICE_STATUS_ICON
} from '@/api/invoice'

import { ElMessage } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import InvoiceDetail from './components/InvoiceDetail.vue'

defineOptions({
  name: 'AdminInvoice'
})

const searchRule = reactive({})
const elSearchFormRef = ref()
const statusFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
const statistics = ref({
  totalInvoices: 0,
  pendingInvoices: 0,
  issuedInvoices: 0,
  failedInvoices: 0,
  totalAmount: 0,
  issuedAmount: 0
})

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getAdminInvoiceList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

// 获取统计数据
const getStatistics = async() => {
  const res = await getAdminInvoiceStatistics()
  if (res.code === 0) {
    statistics.value = res.data
  }
}

// ============== 表格控制部分结束 ===============

// ============== 详情弹窗相关 ===============

const detailDialogVisible = ref(false)
const selectedInvoiceId = ref(null)

// 获取详情信息
const getDetails = async (row) => {
  selectedInvoiceId.value = row.ID
  detailDialogVisible.value = true
}

// 下载发票
const downloadInvoice = (row) => {
  if (row.filePath) {
    window.open(row.filePath, '_blank')
  } else {
    ElMessage.warning('发票文件不存在')
  }
}

// ============== 状态更新相关 ===============

const statusDialogVisible = ref(false)
const currentInvoice = ref(null)

const statusFormData = ref({
  id: null,
  status: '',
  invoiceNumber: '',
  issueDate: null,
  filePath: '',
  notes: ''
})

const statusRule = reactive({
  status: [{
    required: true,
    message: '请选择发票状态',
    trigger: 'change'
  }],
  invoiceNumber: [{
    required: true,
    message: '请输入发票号码',
    trigger: 'blur'
  }]
})

// 打开状态更新弹窗
const updateStatus = (row) => {
  currentInvoice.value = row
  statusFormData.value = {
    id: row.ID,
    status: row.status,
    invoiceNumber: row.invoiceNumber || '',
    issueDate: row.issueDate || null,
    filePath: row.filePath || '',
    notes: row.notes || ''
  }
  statusDialogVisible.value = true
}

// 关闭状态更新弹窗
const closeStatusDialog = () => {
  statusDialogVisible.value = false
  currentInvoice.value = null
  statusFormData.value = {
    id: null,
    status: '',
    invoiceNumber: '',
    issueDate: null,
    filePath: '',
    notes: ''
  }
}

// 提交状态更新
const submitStatusUpdate = async () => {
  statusFormRef.value?.validate(async (valid) => {
    if (!valid) return
    
    const res = await updateInvoiceStatus(statusFormData.value)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '状态更新成功'
      })
      closeStatusDialog()
      getTableData()
      getStatistics()
    }
  })
}

const formatDate = (time) => {
  if (time != null && time !== '') {
    var date = new Date(time)
    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
  } else {
    return ''
  }
}

onMounted(() => {
  getTableData()
  getStatistics()
})
</script>

<style scoped>
.statistics-card {
  cursor: pointer;
  transition: all 0.3s;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.statistics-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.statistics-icon.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.statistics-icon.processing {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.statistics-icon.issued {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.statistics-icon.failed {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 5px;
}
</style>
