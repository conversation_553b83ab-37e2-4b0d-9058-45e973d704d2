package invoice

import (
	"whlxyc.cn/server/global"
)

// InvoiceInfo 开票信息 结构体
type InvoiceInfo struct {
	global.DY_MODEL
	UserID       uint   `json:"userId" form:"userId" gorm:"column:user_id;comment:用户ID;not null"`                                    // 用户ID
	InvoiceType  string `json:"invoiceType" form:"invoiceType" gorm:"column:invoice_type;comment:发票类型;not null"`                   // 发票类型：个人、企业
	InvoiceTitle string `json:"invoiceTitle" form:"invoiceTitle" gorm:"column:invoice_title;comment:发票抬头;not null"`                // 发票抬头
	TaxNumber    string `json:"taxNumber" form:"taxNumber" gorm:"column:tax_number;comment:税号"`                                     // 税号
	CompanyPhone string `json:"companyPhone" form:"companyPhone" gorm:"column:company_phone;comment:公司电话"`                         // 公司电话
	BankName     string `json:"bankName" form:"bankName" gorm:"column:bank_name;comment:开户银行"`                                     // 开户银行
	BankAccount  string `json:"bankAccount" form:"bankAccount" gorm:"column:bank_account;comment:银行账号"`                           // 银行账号
	ContactPhone string `json:"contactPhone" form:"contactPhone" gorm:"column:contact_phone;comment:联系电话;not null"`               // 联系电话
	ContactEmail string `json:"contactEmail" form:"contactEmail" gorm:"column:contact_email;comment:联系邮箱"`                        // 联系邮箱
	IsDefault    bool   `json:"isDefault" form:"isDefault" gorm:"column:is_default;comment:是否默认;default:false"`                   // 是否默认
}

// TableName InvoiceInfo表名
func (InvoiceInfo) TableName() string {
	return "invoice_info"
}

// 发票类型常量
const (
	InvoiceTypePersonal = "personal" // 个人
	InvoiceTypeCompany  = "company"  // 企业
)
