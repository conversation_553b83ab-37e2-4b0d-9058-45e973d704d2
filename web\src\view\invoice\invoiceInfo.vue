<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="发票类型" prop="invoiceType">
          <el-select v-model="searchInfo.invoiceType" clearable placeholder="请选择发票类型">
            <el-option
              v-for="(value, key) in INVOICE_TYPE_TEXT"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增开票信息</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
      >
        <el-table-column align="left" label="日期" prop="CreatedAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="left" label="发票类型" prop="invoiceType" width="120">
          <template #default="scope">
            <el-tag :type="INVOICE_TYPE_COLOR[scope.row.invoiceType]">
              {{ INVOICE_TYPE_TEXT[scope.row.invoiceType] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="发票抬头" prop="invoiceTitle" min-width="200" />
        <el-table-column align="left" label="税号" prop="taxNumber" min-width="180" />
        <el-table-column align="left" label="联系电话" prop="contactPhone" width="120" />
        <el-table-column align="left" label="联系邮箱" prop="contactEmail" min-width="180" />
        <el-table-column align="left" label="默认" prop="isDefault" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.isDefault" type="success">默认</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" min-width="240">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
              <el-icon style="margin-right: 5px"><InfoFilled /></el-icon>
              查看详情
            </el-button>
            <el-button type="primary" link class="table-button" @click="updateInvoiceInfoFunc(scope.row)">
              <el-icon style="margin-right: 5px"><Edit /></el-icon>
              变更
            </el-button>
            <el-button type="danger" link @click="deleteRow(scope.row)">
              <el-icon style="margin-right: 5px"><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-drawer
      destroy-on-close
      size="800"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '添加' : '修改' }}开票信息</span>
          <div>
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="enterDialog">确 定</el-button>
          </div>
        </div>
      </template>

      <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
        <el-form-item label="发票类型:" prop="invoiceType">
          <el-radio-group v-model="formData.invoiceType">
            <el-radio :value="INVOICE_TYPE.PERSONAL">{{ INVOICE_TYPE_TEXT[INVOICE_TYPE.PERSONAL] }}</el-radio>
            <el-radio :value="INVOICE_TYPE.COMPANY">{{ INVOICE_TYPE_TEXT[INVOICE_TYPE.COMPANY] }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票抬头:" prop="invoiceTitle">
          <el-input v-model="formData.invoiceTitle" clearable placeholder="请输入发票抬头" />
        </el-form-item>
        <el-form-item v-if="formData.invoiceType === INVOICE_TYPE.COMPANY" label="税号:" prop="taxNumber">
          <el-input v-model="formData.taxNumber" clearable placeholder="请输入税号" />
        </el-form-item>
        <el-form-item v-if="formData.invoiceType === INVOICE_TYPE.COMPANY" label="公司电话:" prop="companyPhone">
          <el-input v-model="formData.companyPhone" clearable placeholder="请输入公司电话" />
        </el-form-item>
        <el-form-item v-if="formData.invoiceType === INVOICE_TYPE.COMPANY" label="开户银行:" prop="bankName">
          <el-input v-model="formData.bankName" clearable placeholder="请输入开户银行" />
        </el-form-item>
        <el-form-item v-if="formData.invoiceType === INVOICE_TYPE.COMPANY" label="银行账号:" prop="bankAccount">
          <el-input v-model="formData.bankAccount" clearable placeholder="请输入银行账号" />
        </el-form-item>
        <el-form-item label="联系电话:" prop="contactPhone">
          <el-input v-model="formData.contactPhone" clearable placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="联系邮箱:" prop="contactEmail">
          <el-input v-model="formData.contactEmail" clearable placeholder="请输入联系邮箱" />
        </el-form-item>
        <el-form-item label="设为默认:" prop="isDefault">
          <el-switch v-model="formData.isDefault" />
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 开票信息详情弹窗 -->
    <InvoiceInfoDetail
      v-model:visible="detailDialogVisible"
      :invoice-info-id="selectedInvoiceInfoId"
      @edit="handleEditFromDetail"
    />
  </div>
</template>

<script setup>
import {
  createInvoiceInfo,
  deleteInvoiceInfo,
  updateInvoiceInfo,
  getInvoiceInfoById,
  getInvoiceInfoList,
  INVOICE_TYPE,
  INVOICE_TYPE_TEXT,
  INVOICE_TYPE_COLOR
} from '@/api/invoice'

import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import InvoiceInfoDetail from './components/InvoiceInfoDetail.vue'

defineOptions({
  name: 'InvoiceInfo'
})

const methodMap = {
  create: createInvoiceInfo,
  update: updateInvoiceInfo
}

const searchRule = reactive({})
const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getInvoiceInfoList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// ============== 详情弹窗相关 ===============

const detailDialogVisible = ref(false)
const selectedInvoiceInfoId = ref(null)

// 获取详情信息
const getDetails = async (row) => {
  selectedInvoiceInfoId.value = row.ID
  detailDialogVisible.value = true
}

// 从详情弹窗编辑
const handleEditFromDetail = (invoiceInfoData) => {
  updateInvoiceInfoFunc({ ID: invoiceInfoData.ID })
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteInvoiceInfoFunc(row)
  })
}

// 批量删除控制标记
const deleteInvoiceInfoFunc = async (row) => {
  const res = await deleteInvoiceInfo({ id: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    invoiceType: INVOICE_TYPE.PERSONAL,
    invoiceTitle: '',
    taxNumber: '',
    companyPhone: '',
    bankName: '',
    bankAccount: '',
    contactPhone: '',
    contactEmail: '',
    isDefault: false
  }
}
// 弹窗确定
const enterDialog = async () => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    let res
    switch (type.value) {
      case 'create':
        res = await createInvoiceInfo(formData.value)
        break
      case 'update':
        res = await updateInvoiceInfo(formData.value)
        break
      default:
        res = await createInvoiceInfo(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getTableData()
    }
  })
}

const updateInvoiceInfoFunc = async (row) => {
  const res = await getInvoiceInfoById(row.ID)
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data
    dialogFormVisible.value = true
  }
}

const type = ref('')
const formData = ref({
  invoiceType: INVOICE_TYPE.PERSONAL,
  invoiceTitle: '',
  taxNumber: '',
  companyPhone: '',
  bankName: '',
  bankAccount: '',
  contactPhone: '',
  contactEmail: '',
  isDefault: false
})

// 验证规则
const rule = reactive({
  invoiceType: [{
    required: true,
    message: '请选择发票类型',
    trigger: 'blur'
  }],
  invoiceTitle: [{
    required: true,
    message: '请输入发票抬头',
    trigger: 'blur'
  }],
  contactPhone: [{
    required: true,
    message: '请输入联系电话',
    trigger: 'blur'
  }]
})

const formatDate = (time) => {
  if (time != null && time !== '') {
    var date = new Date(time)
    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
  } else {
    return ''
  }
}
</script>

<style></style>
