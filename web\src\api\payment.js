import service from '@/utils/request'

// ==================== 支付记录管理 API ====================

// @Summary 创建支付记录
// @Produce application/json
// @Param data body CreatePaymentRequest true "创建支付记录"
// @Router /payment/createPayment [post]
export const createPayment = (data) => {
  return service({
    url: '/payment/createPayment',
    method: 'post',
    data: data
  })
}

// @Summary 更新支付记录
// @Produce application/json
// @Param data body UpdatePaymentRequest true "更新支付记录"
// @Router /payment/updatePayment [put]
export const updatePayment = (data) => {
  return service({
    url: '/payment/updatePayment',
    method: 'put',
    data: data
  })
}

// @Summary 管理员更新支付记录
// @Produce application/json
// @Param data body UpdatePaymentRequest true "更新支付记录"
// @Router /payment/adminUpdatePayment [put]
export const adminUpdatePayment = (data) => {
  return service({
    url: '/payment/adminUpdatePayment',
    method: 'put',
    data: data
  })
}

// @Summary 删除支付记录
// @Produce application/json
// @Param data body DeletePaymentRequest true "删除支付记录"
// @Router /payment/deletePayment [delete]
export const deletePayment = (data) => {
  return service({
    url: '/payment/deletePayment',
    method: 'delete',
    data: data
  })
}

// @Summary 管理员删除支付记录
// @Produce application/json
// @Param data body DeletePaymentRequest true "删除支付记录"
// @Router /payment/adminDeletePayment [delete]
export const adminDeletePayment = (data) => {
  return service({
    url: '/payment/adminDeletePayment',
    method: 'delete',
    data: data
  })
}

// @Summary 根据ID获取支付记录
// @Produce application/json
// @Param id query uint true "支付记录ID"
// @Router /payment/findPayment [get]
export const getPaymentById = (id) => {
  return service({
    url: '/payment/findPayment',
    method: 'get',
    params: { id }
  })
}

// @Summary 管理员根据ID获取支付记录
// @Produce application/json
// @Param id query uint true "支付记录ID"
// @Router /payment/adminFindPayment [get]
export const adminGetPaymentById = (id) => {
  return service({
    url: '/payment/adminFindPayment',
    method: 'get',
    params: { id }
  })
}

// @Summary 获取支付记录列表
// @Produce application/json
// @Param data query PaymentSearch true "获取支付记录列表"
// @Router /payment/getPaymentList [get]
export const getPaymentList = (params) => {
  return service({
    url: '/payment/getPaymentList',
    method: 'get',
    params: params
  })
}

// @Summary 管理员获取支付记录列表
// @Produce application/json
// @Param data query PaymentSearch true "获取支付记录列表"
// @Router /payment/adminGetPaymentList [get]
export const adminGetPaymentList = (params) => {
  return service({
    url: '/payment/adminGetPaymentList',
    method: 'get',
    params: params
  })
}

// @Summary 退款
// @Produce application/json
// @Param data body RefundPaymentRequest true "退款"
// @Router /payment/refundPayment [post]
export const refundPayment = (data) => {
  return service({
    url: '/payment/refundPayment',
    method: 'post',
    data: data
  })
}

// @Summary 管理员退款
// @Produce application/json
// @Param data body RefundPaymentRequest true "退款"
// @Router /payment/adminRefundPayment [post]
export const adminRefundPayment = (data) => {
  return service({
    url: '/payment/adminRefundPayment',
    method: 'post',
    data: data
  })
}

// @Summary 获取支付统计
// @Produce application/json
// @Param data query PaymentStatisticsRequest true "获取支付统计"
// @Router /payment/getPaymentStatistics [get]
export const getPaymentStatistics = (params) => {
  return service({
    url: '/payment/getPaymentStatistics',
    method: 'get',
    params: params
  })
}

// @Summary 管理员获取支付统计
// @Produce application/json
// @Param data query PaymentStatisticsRequest true "获取支付统计"
// @Router /payment/adminGetPaymentStatistics [get]
export const adminGetPaymentStatistics = (params) => {
  return service({
    url: '/payment/adminGetPaymentStatistics',
    method: 'get',
    params: params
  })
}

// @Summary 获取可用于开票的支付记录
// @Produce application/json
// @Router /payment/getAvailablePaymentsForInvoice [get]
export const getAvailablePaymentsForInvoice = () => {
  return service({
    url: '/payment/getAvailablePaymentsForInvoice',
    method: 'get'
  })
}

// ==================== 常量定义 ====================

// 支付方式常量
export const PAYMENT_METHOD = {
  ALIPAY: 'alipay_web',  // 支付宝
  WECHAT: 'wechat_native',  // 微信支付
  BANK: 'bank',      // 银行转账
  CASH: 'cash'       // 现金支付
}

// 支付状态常量
export const PAYMENT_STATUS = {
  PENDING: 'pending',   // 待支付
  SUCCESS: 'success',   // 支付成功
  FAILED: 'failed',     // 支付失败
  REFUNDED: 'refunded'  // 已退款
}

// 支付方式显示文本映射
export const PAYMENT_METHOD_TEXT = {
  [PAYMENT_METHOD.ALIPAY]: '支付宝扫码',
  [PAYMENT_METHOD.WECHAT]: '微信支付扫码',
  [PAYMENT_METHOD.BANK]: '银行转账',
  [PAYMENT_METHOD.CASH]: '现金支付'
}

// 支付状态显示文本映射
export const PAYMENT_STATUS_TEXT = {
  [PAYMENT_STATUS.PENDING]: '待支付',
  [PAYMENT_STATUS.SUCCESS]: '支付成功',
  [PAYMENT_STATUS.FAILED]: '支付失败',
  [PAYMENT_STATUS.REFUNDED]: '已退款'
}

// 支付方式颜色映射
export const PAYMENT_METHOD_COLOR = {
  [PAYMENT_METHOD.ALIPAY]: 'primary',
  [PAYMENT_METHOD.WECHAT]: 'success',
  [PAYMENT_METHOD.BANK]: 'warning',
  [PAYMENT_METHOD.CASH]: 'info'
}

// 支付状态颜色映射
export const PAYMENT_STATUS_COLOR = {
  [PAYMENT_STATUS.PENDING]: 'warning',
  [PAYMENT_STATUS.SUCCESS]: 'success',
  [PAYMENT_STATUS.FAILED]: 'danger',
  [PAYMENT_STATUS.REFUNDED]: 'info'
}

// 支付状态图标映射
export const PAYMENT_STATUS_ICON = {
  [PAYMENT_STATUS.PENDING]: 'Clock',
  [PAYMENT_STATUS.SUCCESS]: 'Check',
  [PAYMENT_STATUS.FAILED]: 'Close',
  [PAYMENT_STATUS.REFUNDED]: 'RefreshLeft'
}
