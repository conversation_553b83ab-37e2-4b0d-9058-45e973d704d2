<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑开票信息' : '新增开票信息'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="100px">
      <el-form-item label="发票类型:" prop="invoiceType">
        <el-radio-group v-model="formData.invoiceType">
          <el-radio :value="INVOICE_TYPE.PERSONAL">
            {{ INVOICE_TYPE_TEXT[INVOICE_TYPE.PERSONAL] }}
          </el-radio>
          <el-radio :value="INVOICE_TYPE.COMPANY">
            {{ INVOICE_TYPE_TEXT[INVOICE_TYPE.COMPANY] }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="发票抬头:" prop="invoiceTitle">
        <el-input 
          v-model="formData.invoiceTitle" 
          clearable 
          placeholder="请输入发票抬头" 
        />
      </el-form-item>
      
      <template v-if="formData.invoiceType === INVOICE_TYPE.COMPANY">
        <el-form-item label="税号:" prop="taxNumber">
          <el-input 
            v-model="formData.taxNumber" 
            clearable 
            placeholder="请输入税号" 
          />
        </el-form-item>
        
        <el-form-item label="公司电话:" prop="companyPhone">
          <el-input 
            v-model="formData.companyPhone" 
            clearable 
            placeholder="请输入公司电话" 
          />
        </el-form-item>
        
        <el-form-item label="开户银行:" prop="bankName">
          <el-input 
            v-model="formData.bankName" 
            clearable 
            placeholder="请输入开户银行" 
          />
        </el-form-item>
        
        <el-form-item label="银行账号:" prop="bankAccount">
          <el-input 
            v-model="formData.bankAccount" 
            clearable 
            placeholder="请输入银行账号" 
          />
        </el-form-item>
      </template>
      
      <el-form-item label="联系电话:" prop="contactPhone">
        <el-input 
          v-model="formData.contactPhone" 
          clearable 
          placeholder="请输入联系电话" 
        />
      </el-form-item>
      
      <el-form-item label="联系邮箱:" prop="contactEmail">
        <el-input 
          v-model="formData.contactEmail" 
          clearable 
          placeholder="请输入联系邮箱" 
        />
      </el-form-item>
      
      <el-form-item label="设为默认:" prop="isDefault">
        <el-switch v-model="formData.isDefault" />
        <div class="text-sm text-gray-500 mt-1">
          设为默认后，申请开票时会自动选择此开票信息
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更 新' : '创 建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  createInvoiceInfo, 
  updateInvoiceInfo,
  INVOICE_TYPE,
  INVOICE_TYPE_TEXT 
} from '@/api/invoice'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  invoiceInfo: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const formRef = ref()
const loading = ref(false)

const isEdit = computed(() => {
  return props.invoiceInfo && props.invoiceInfo.ID
})

const formData = ref({
  invoiceType: INVOICE_TYPE.PERSONAL,
  invoiceTitle: '',
  taxNumber: '',
  companyPhone: '',
  bankName: '',
  bankAccount: '',
  contactPhone: '',
  contactEmail: '',
  isDefault: false
})

const rules = reactive({
  invoiceType: [{
    required: true,
    message: '请选择发票类型',
    trigger: 'change'
  }],
  invoiceTitle: [{
    required: true,
    message: '请输入发票抬头',
    trigger: 'blur'
  }],
  taxNumber: [{
    validator: (rule, value, callback) => {
      if (formData.value.invoiceType === INVOICE_TYPE.COMPANY && !value) {
        callback(new Error('企业发票必须填写税号'))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }],
  contactPhone: [{
    required: true,
    message: '请输入联系电话',
    trigger: 'blur'
  }, {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码',
    trigger: 'blur'
  }],
  contactEmail: [{
    type: 'email',
    message: '请输入正确的邮箱地址',
    trigger: 'blur'
  }]
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// 监听开票信息变化
watch(() => props.invoiceInfo, (newVal) => {
  if (newVal && props.visible) {
    initForm()
  }
}, { deep: true })

// 初始化表单
const initForm = () => {
  if (isEdit.value) {
    // 编辑模式，填充现有数据
    formData.value = {
      id: props.invoiceInfo.ID,
      invoiceType: props.invoiceInfo.invoiceType || INVOICE_TYPE.PERSONAL,
      invoiceTitle: props.invoiceInfo.invoiceTitle || '',
      taxNumber: props.invoiceInfo.taxNumber || '',
      companyPhone: props.invoiceInfo.companyPhone || '',
      bankName: props.invoiceInfo.bankName || '',
      bankAccount: props.invoiceInfo.bankAccount || '',
      contactPhone: props.invoiceInfo.contactPhone || '',
      contactEmail: props.invoiceInfo.contactEmail || '',
      isDefault: props.invoiceInfo.isDefault || false
    }
  } else {
    // 新增模式，重置表单
    formData.value = {
      invoiceType: INVOICE_TYPE.PERSONAL,
      invoiceTitle: '',
      taxNumber: '',
      companyPhone: '',
      bankName: '',
      bankAccount: '',
      contactPhone: '',
      contactEmail: '',
      isDefault: false
    }
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return
    
    loading.value = true
    
    let res
    if (isEdit.value) {
      res = await updateInvoiceInfo(formData.value)
    } else {
      res = await createInvoiceInfo(formData.value)
    }
    
    if (res.code === 0) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      emit('success', res.data)
      handleClose()
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
