<template>
  <el-dialog
    v-model="visible"
    title="发票详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="invoice-detail">
      <!-- 发票基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <el-icon><Document /></el-icon>
          发票基本信息
        </h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发票号码">
            <span v-if="invoiceData.invoiceNumber" class="invoice-number">
              {{ invoiceData.invoiceNumber }}
            </span>
            <el-tag v-else type="info">未生成</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发票状态">
            <el-tag 
              :type="INVOICE_STATUS_COLOR[invoiceData.status]" 
              :icon="INVOICE_STATUS_ICON[invoiceData.status]"
            >
              {{ INVOICE_STATUS_TEXT[invoiceData.status] }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发票抬头">
            {{ invoiceData.invoiceTitle || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="税号">
            {{ invoiceData.taxNumber || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="发票金额">
            <span class="amount">¥{{ invoiceData.amount || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="申请日期">
            {{ formatDateTime(invoiceData.CreatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="开票日期">
            <span v-if="invoiceData.issueDate">
              {{ formatDateTime(invoiceData.issueDate) }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </el-descriptions-item>
          <el-descriptions-item label="用户信息" v-if="invoiceData.user">
            <div class="user-info">
              <div>{{ invoiceData.user.nickName || invoiceData.user.username }}</div>
              <div class="text-sm text-gray-500">ID: {{ invoiceData.user.ID }}</div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 支付信息 -->
      <div class="detail-section" v-if="paymentInfo">
        <h3 class="section-title">
          <el-icon><CreditCard /></el-icon>
          支付信息
        </h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="支付ID">
            {{ paymentInfo.id || invoiceData.paymentId }}
          </el-descriptions-item>
          <el-descriptions-item label="支付金额">
            <span class="amount">¥{{ paymentInfo.amount || invoiceData.amount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            {{ paymentInfo.paymentMethod || '线上支付' }}
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">
            {{ formatDateTime(paymentInfo.paidAt || paymentInfo.createdAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 发票文件 -->
      <div class="detail-section" v-if="invoiceData.status === INVOICE_STATUS.ISSUED">
        <h3 class="section-title">
          <el-icon><Folder /></el-icon>
          发票文件
        </h3>
        <div class="file-section">
          <div v-if="invoiceData.filePath" class="file-item">
            <div class="file-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <div class="file-details">
                <div class="file-name">{{ getFileName(invoiceData.filePath) }}</div>
                <div class="file-meta">发票文件 • {{ formatDateTime(invoiceData.issueDate) }}</div>
              </div>
            </div>
            <div class="file-actions">
              <el-button type="primary" size="small" @click="previewFile">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button type="success" size="small" @click="downloadFile">
                <el-icon><Download /></el-icon>
                下载
              </el-button>
            </div>
          </div>
          <div v-else class="no-file">
            <el-empty description="暂无发票文件" :image-size="80" />
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="detail-section" v-if="invoiceData.notes">
        <h3 class="section-title">
          <el-icon><EditPen /></el-icon>
          备注信息
        </h3>
        <div class="notes-content">
          {{ invoiceData.notes }}
        </div>
      </div>

      <!-- 操作历史 -->
      <div class="detail-section">
        <h3 class="section-title">
          <el-icon><Clock /></el-icon>
          操作历史
        </h3>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in operationHistory"
            :key="index"
            :timestamp="item.timestamp"
            :type="item.type"
            :icon="item.icon"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ item.title }}</div>
              <div class="timeline-description" v-if="item.description">
                {{ item.description }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="invoiceData.status === INVOICE_STATUS.ISSUED && invoiceData.filePath"
          type="primary" 
          @click="downloadFile"
        >
          <el-icon><Download /></el-icon>
          下载发票
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getInvoiceById,
  INVOICE_STATUS,
  INVOICE_STATUS_TEXT,
  INVOICE_STATUS_COLOR,
  INVOICE_STATUS_ICON
} from '@/api/invoice'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  invoiceId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:visible'])

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const loading = ref(false)
const invoiceData = ref({})
const paymentInfo = ref(null)

// 操作历史
const operationHistory = computed(() => {
  const history = []
  
  if (invoiceData.value.CreatedAt) {
    history.push({
      timestamp: formatDateTime(invoiceData.value.CreatedAt),
      title: '申请开票',
      description: '用户提交开票申请',
      type: 'primary',
      icon: 'Plus'
    })
  }
  
  if (invoiceData.value.status === INVOICE_STATUS.PROCESSING) {
    history.push({
      timestamp: formatDateTime(invoiceData.value.UpdatedAt),
      title: '开始处理',
      description: '管理员开始处理开票申请',
      type: 'warning',
      icon: 'Loading'
    })
  }
  
  if (invoiceData.value.status === INVOICE_STATUS.ISSUED && invoiceData.value.issueDate) {
    history.push({
      timestamp: formatDateTime(invoiceData.value.issueDate),
      title: '开票完成',
      description: `发票号码：${invoiceData.value.invoiceNumber}`,
      type: 'success',
      icon: 'Check'
    })
  }
  
  if (invoiceData.value.status === INVOICE_STATUS.FAILED) {
    history.push({
      timestamp: formatDateTime(invoiceData.value.UpdatedAt),
      title: '开票失败',
      description: invoiceData.value.notes || '开票处理失败',
      type: 'danger',
      icon: 'Close'
    })
  }
  
  return history.reverse() // 最新的在前面
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.invoiceId) {
    loadInvoiceDetail()
  }
})

// 加载发票详情
const loadInvoiceDetail = async () => {
  if (!props.invoiceId) return
  
  try {
    loading.value = true
    const res = await getInvoiceById(props.invoiceId)
    
    if (res.code === 0) {
      invoiceData.value = res.data
      // 这里可以加载支付信息
      // loadPaymentInfo(res.data.paymentId)
    }
  } catch (error) {
    console.error('加载发票详情失败:', error)
    ElMessage.error('加载发票详情失败')
  } finally {
    loading.value = false
  }
}

// 获取文件名
const getFileName = (filePath) => {
  if (!filePath) return ''
  return filePath.split('/').pop() || filePath.split('\\').pop() || '发票文件'
}

// 预览文件
const previewFile = () => {
  if (invoiceData.value.filePath) {
    window.open(invoiceData.value.filePath, '_blank')
  }
}

// 下载文件
const downloadFile = () => {
  if (invoiceData.value.filePath) {
    const link = document.createElement('a')
    link.href = invoiceData.value.filePath
    link.download = getFileName(invoiceData.value.filePath)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } else {
    ElMessage.warning('发票文件不存在')
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  invoiceData.value = {}
  paymentInfo.value = null
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.invoice-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.invoice-number {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #409eff;
}

.amount {
  font-size: 16px;
  font-weight: bold;
  color: #f56c6c;
}

.user-info {
  line-height: 1.4;
}

.file-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
}

.file-details {
  line-height: 1.4;
}

.file-name {
  font-weight: 500;
  color: #303133;
}

.file-meta {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.no-file {
  text-align: center;
  padding: 20px;
}

.notes-content {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
  color: #606266;
}

.timeline-content {
  line-height: 1.4;
}

.timeline-title {
  font-weight: 500;
  color: #303133;
}

.timeline-description {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
