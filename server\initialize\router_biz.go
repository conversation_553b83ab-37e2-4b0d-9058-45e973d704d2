package initialize

import (
	"github.com/gin-gonic/gin"
	"whlxyc.cn/server/router"
)

// 占位方法，保证文件可以正确加载，避免go空变量检测报错，请勿删除。
func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}

func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]

	// 注册会议路由
	meetingRouter := router.RouterGroupApp.Meeting
	meetingRouter.MeetingRouter.InitMeetingRouter(privateGroup, publicGroup)
	meetingRouter.PaymentRouter.InitPaymentRouter(privateGroup, publicGroup)

	// 注册开票路由
	invoiceRouter := router.RouterGroupApp.Invoice
	invoiceRouter.InvoiceRouter.InitInvoiceRouter(privateGroup)

	holder(publicGroup, privateGroup)
}
