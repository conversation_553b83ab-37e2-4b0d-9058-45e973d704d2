package request

import (
	"time"
	"whlxyc.cn/server/model/common/request"
)

// CreateInvoiceInfoRequest 创建开票信息请求
type CreateInvoiceInfoRequest struct {
	InvoiceType  string `json:"invoiceType" binding:"required"`  // 发票类型
	InvoiceTitle string `json:"invoiceTitle" binding:"required"` // 发票抬头
	TaxNumber    string `json:"taxNumber"`                       // 税号
	CompanyPhone string `json:"companyPhone"`                    // 公司电话
	BankName     string `json:"bankName"`                        // 开户银行
	BankAccount  string `json:"bankAccount"`                     // 银行账号
	ContactPhone string `json:"contactPhone" binding:"required"` // 联系电话
	ContactEmail string `json:"contactEmail"`                    // 联系邮箱
	IsDefault    bool   `json:"isDefault"`                       // 是否默认
}

// UpdateInvoiceInfoRequest 更新开票信息请求
type UpdateInvoiceInfoRequest struct {
	ID           uint   `json:"id" binding:"required"`           // ID
	InvoiceType  string `json:"invoiceType" binding:"required"`  // 发票类型
	InvoiceTitle string `json:"invoiceTitle" binding:"required"` // 发票抬头
	TaxNumber    string `json:"taxNumber"`                       // 税号
	CompanyPhone string `json:"companyPhone"`                    // 公司电话
	BankName     string `json:"bankName"`                        // 开户银行
	BankAccount  string `json:"bankAccount"`                     // 银行账号
	ContactPhone string `json:"contactPhone" binding:"required"` // 联系电话
	ContactEmail string `json:"contactEmail"`                    // 联系邮箱
	IsDefault    bool   `json:"isDefault"`                       // 是否默认
}

// ApplyInvoiceRequest 申请开票请求
type ApplyInvoiceRequest struct {
	PaymentID       uint   `json:"paymentId" binding:"required"`       // 支付ID
	InvoiceInfoID   uint   `json:"invoiceInfoId" binding:"required"`   // 开票信息ID
	InvoiceTitle    string `json:"invoiceTitle" binding:"required"`    // 发票抬头
	TaxNumber       string `json:"taxNumber"`                          // 税号
	Amount          float64 `json:"amount" binding:"required"`          // 发票金额
	Notes           string `json:"notes"`                              // 备注
}

// UpdateInvoiceStatusRequest 更新发票状态请求
type UpdateInvoiceStatusRequest struct {
	ID            uint       `json:"id" binding:"required"`     // 发票ID
	Status        string     `json:"status" binding:"required"` // 发票状态
	InvoiceNumber string     `json:"invoiceNumber"`             // 发票号码
	IssueDate     *time.Time `json:"issueDate"`                 // 开票日期
	FilePath      string     `json:"filePath"`                  // 发票文件路径
	Notes         string     `json:"notes"`                     // 备注
}

// InvoiceInfoSearch 开票信息搜索
type InvoiceInfoSearch struct {
	request.PageInfo
	InvoiceType string `json:"invoiceType" form:"invoiceType"` // 发票类型
}

// InvoiceSearch 发票搜索
type InvoiceSearch struct {
	request.PageInfo
	Status    string `json:"status" form:"status"`       // 发票状态
	StartDate string `json:"startDate" form:"startDate"` // 开始日期
	EndDate   string `json:"endDate" form:"endDate"`     // 结束日期
	UserID    *uint  `json:"userId" form:"userId"`       // 用户ID
}

// DeleteInvoiceInfoRequest 删除开票信息请求
type DeleteInvoiceInfoRequest struct {
	ID uint `json:"id" binding:"required"` // ID
}

// DeleteInvoiceRequest 删除发票请求
type DeleteInvoiceRequest struct {
	ID uint `json:"id" binding:"required"` // ID
}
