package meeting

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"whlxyc.cn/server/global"
	"whlxyc.cn/server/model/common/response"
	"whlxyc.cn/server/model/meeting/request"
	"whlxyc.cn/server/utils"
)

type MeetingApi struct{}

// CreateMeeting 创建会议
// @Tags Meeting
// @Summary 创建会议
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CreateMeetingRequest true "创建会议"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /meeting/createMeeting [post]
func (m *MeetingApi) CreateMeeting(c *gin.Context) {
	var req request.CreateMeetingRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	meeting, err := meetingService.CreateMeeting(&req, userID)
	if err != nil {
		global.DY_LOG.Error("创建会议失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithData(meeting, c)
}

// UpdateMeeting 更新会议
// @Tags Meeting
// @Summary 更新会议
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateMeetingRequest true "更新会议"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /meeting/updateMeeting [put]
func (m *MeetingApi) UpdateMeeting(c *gin.Context) {
	var req request.UpdateMeetingRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = meetingService.UpdateMeeting(&req, userID)
	if err != nil {
		global.DY_LOG.Error("更新会议失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteMeeting 删除会议
// @Tags Meeting
// @Summary 删除会议
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.DeleteMeetingRequest true "删除会议"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /meeting/deleteMeeting [delete]
func (m *MeetingApi) DeleteMeeting(c *gin.Context) {
	var req request.DeleteMeetingRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = meetingService.DeleteMeeting(req.ID, userID)
	if err != nil {
		global.DY_LOG.Error("删除会议失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetMeetingByID 根据ID获取会议详情
// @Tags Meeting
// @Summary 根据ID获取会议详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "会议ID"
// @Success 200 {object} response.Response{data=response.MeetingDetailResponse} "获取成功"
// @Router /meeting/findMeeting [get]
func (m *MeetingApi) GetMeetingByID(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	meeting, err := meetingService.GetMeetingByID(uint(id))
	if err != nil {
		global.DY_LOG.Error("查询会议失败!", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	response.OkWithData(meeting, c)
}

// GetMeetingList 获取会议列表
// @Tags Meeting
// @Summary 获取会议列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.MeetingSearch true "分页获取会议列表"
// @Success 200 {object} response.Response{data=response.MeetingListResponse} "获取成功"
// @Router /meeting/getMeetingList [get]
func (m *MeetingApi) GetMeetingList(c *gin.Context) {
	var req request.MeetingSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	total, meetings, err := meetingService.GetMeetingList(&req)
	if err != nil {
		global.DY_LOG.Error("获取会议列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(response.PageResult{
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     meetings,
		Total:    total,
	}, c)
}

// UpdateParticipantStatus 更新参与者状态
// @Tags Meeting
// @Summary 更新参与者状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateParticipantStatusRequest true "更新参与者状态"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /meeting/updateParticipantStatus [put]
func (m *MeetingApi) UpdateParticipantStatus(c *gin.Context) {
	var req request.UpdateParticipantStatusRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = meetingService.UpdateParticipantStatus(&req, userID)
	if err != nil {
		global.DY_LOG.Error("更新参与者状态失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// GetUserMeetings 获取用户会议列表
// @Tags Meeting
// @Summary 获取用户会议列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.UserMeetingRequest true "获取用户会议列表"
// @Success 200 {object} response.Response{data=response.MeetingListResponse} "获取成功"
// @Router /meeting/getUserMeetings [get]
func (m *MeetingApi) GetUserMeetings(c *gin.Context) {
	var req request.UserMeetingRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	meetings, err := meetingService.GetUserMeetings(&req, userID)
	if err != nil {
		global.DY_LOG.Error("获取用户会议列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(meetings, c)
}

// GetMeetingStatistics 获取会议统计信息
// @Tags Meeting
// @Summary 获取会议统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.MeetingStatisticsResponse} "获取成功"
// @Router /meeting/getMeetingStatistics [get]
func (m *MeetingApi) GetMeetingStatistics(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)

	stats, err := meetingService.GetMeetingStatistics(userID)
	if err != nil {
		global.DY_LOG.Error("获取会议统计信息失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(stats, c)
}
